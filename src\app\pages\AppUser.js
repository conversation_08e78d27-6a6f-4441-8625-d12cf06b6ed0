// @ts-nocheck
import React, { useState, useRef, useEffect } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { EHS_ROLE_URL, EPTW_ROLE_URL, EXTERNAL_USERS_URL, EXTERNAL_USER_DELETE, EXTERNAL_USER_MFA_RESET, EXTERNAL_USER_PASSWORD_RESET, INCIDENT_ROLE_URL, INSPECTION_ROLE_URL, INTERNAL_USERS_URL, LOCATION1_URL, PLANT_ROLE_URL, USERS_URL, USERS_URL_WITH_ID } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import { userColumns, tableOptions } from './TableColumns';
import CardOverlay from './CardOverlay';
import FilterLocation from './FilterLocation';
import { DropzoneArea } from 'material-ui-dropzone'
import { LinearProgress } from '@material-ui/core';
import * as XLSX from 'xlsx';
import AllFilterLocation from './AllFilterLocation';
import { useSelector } from 'react-redux'
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;

const thumbsContainer = {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 16
};

const thumb = {
    display: 'inline-flex',
    borderRadius: 2,
    border: '1px solid #eaeaea',
    marginBottom: 8,
    marginRight: 8,
    width: 100,
    height: 100,
    padding: 4,
    boxSizing: 'border-box'
};

const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const AppUser = () => {
    const defaultMaterialTheme = createTheme();
    const me = useSelector(state => state.login.user);
    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }

    const [mdShow, setMdShow] = useState(false);
    const [userShow, setUserShow] = useState(false);
    const [excelShow, setExcelShow] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const history = useHistory();
    const uName = useRef();
    const uEmail = useRef();
    const uCompany = useRef();
    const country = useRef();
    const uPassword = useRef();
    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
    const [selectedRoles, setSelectedRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
    const [selectedUserId, setSelectedUserId] = useState({ id: "", email: "", name: "" });
    useEffect(() => {
        getCountry();
        getEhsRole();
        getEptwRole();
        getIncidentRole();
        getInspectionRole();
        getPlantRole();

    }, [])


    const getCountry = async () => {
        const response = await API.get(LOCATION1_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, country: response.data } })
        }
    }

    const getEhsRole = async () => {
        const response = await API.get(EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, ehs: response.data } })
        }
    }

    const getEptwRole = async () => {
        const response = await API.get(EPTW_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, eptw: response.data } })
        }
    }

    const getIncidentRole = async () => {
        const response = await API.get(INCIDENT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, incident: response.data } })
        }
    }

    const getInspectionRole = async () => {
        const response = await API.get(INSPECTION_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, inspection: response.data } })
        }
    }

    const getPlantRole = async () => {
        const response = await API.get(PLANT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, plant: response.data } })
        }
    }
    const roleCountryMap = {
        "India (IN)": "India",
        "UK (UK)": "UK",
        "Singapore (SG)": "Singapore",
        "Thailand (TH)": "Thailand",
        "Korea (KR)": "Korea",
        "Philippines (PH)": "Philippines"
    };
    const roles = me.validationRoles;

    const allowedCountries = roles
        .map(role => roleCountryMap[role.name])
        .filter(country => country !== undefined);


    const [data, setData] = useState([])
    useEffect(() => {
        getUsersData();
    }, [])

    const getUsersData = async () => {
        const response = await API.get(EXTERNAL_USERS_URL);
        if (response.status === 200) {
            setData(response.data.filter(i => i.status !== false).filter(i => allowedCountries.includes(i.country)).sort((a, b) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase())));


        }
    }



    const viewAssignPermission = async (id, email, name) => {
        const response = await API.get(USERS_URL_WITH_ID(id))
        if (response.status === 200) {

            if (response.data.customRoles)
                setSelectedRoles(response.data.customRoles)
            else
                setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
            setSelectedUserId({ id: id, email: email, name: name })
            setMdShow(true)

        }
    }





    const handleRoleChange = (e, category) => {
        const roleId = e.target.value;
        console.log(roleId)

        setSelectedRoles((prevRoles) => {
            const categoryRoles = prevRoles[category];
            if (e.target.checked) {
                // Add the role to the selected roles
                return {
                    ...prevRoles,
                    [category]: [...categoryRoles, roleId],
                };
            } else {
                // Remove the role from the selected roles
                return {
                    ...prevRoles,
                    [category]: categoryRoles.filter((id) => id !== roleId),
                };
            }
        });
    };

    const handleAssignSubmit = async () => {
        const id = selectedUserId.id;
        const response = await API.patch(USERS_URL_WITH_ID(id), { email: selectedUserId.email, customRoles: selectedRoles })
        if (response.status === 204) {
            setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
            setSelectedUserId({ id: "", email: "", name: "" })
            setMdShow(false)
            cogoToast.info('Assigned', { position: 'top-right' })

        }
    }

    const [progress, setProgress] = useState(0);

    const uploadBulkUser = async () => {
        setExcelShow(false);
        if (files && files[0]) {
            setProgress(0);
            const reader = new FileReader();
            reader.onload = (evt) => {
                // Parse data
                const bstr = evt.target.result;
                const wb = XLSX.read(bstr, { type: 'binary' });
                // Get first worksheet
                const wsname = wb.SheetNames[0];
                const ws = wb.Sheets[wsname];
                // Convert array of arrays
                const data = XLSX.utils.sheet_to_json(ws, { header: 1 });
                // Remove the first row (headers)
                data.shift();
                // Process each row, for example, you could print it
                data.forEach((row, index) => {
                    console.log(row);
                    // Calculate progress
                    createBulkUser(row[0], row[1], row[2], row[3])
                    setProgress((index + 1) / data.length * 100);
                });
            };
            reader.readAsBinaryString(files[0]);
        }
        getUsersData();
        cogoToast.success('Users Added!')
    }

    const createBulkUser = async (firstName, email, company, password) => {
        const response = await API.post(EXTERNAL_USERS_URL, {
            firstName: firstName,
            email: email,
            company: company,
            password: password,


        })
        if (response.status === 200) {
            return true
        } else {
            return false
        }
    }

    const resetPassword = async (email) => {
        const response = await API.post(EXTERNAL_USER_PASSWORD_RESET, {
            email: email
        })

        if (response.status === 200) {
            cogoToast.success('New password has been generated and mailed!')
        } else {
            cogoToast.error('Something went wrong!')
        }
    }

    const resetMFA = async (email) => {
        const response = await API.post(EXTERNAL_USER_MFA_RESET, {
            email: email
        })

        if (response.status === 200) {
            cogoToast.success('MFA has been reset!')
        } else {
            cogoToast.error('Something went wrong!')
        }
    }

    const deleteUser = async (email) => {
        const response = await API.post(EXTERNAL_USER_DELETE, {
            email: email
        })

        if (response.status === 200) {
            cogoToast.success('User Deleted!')
        } else {
            cogoToast.error('Something went wrong!')
        }
    }

    const createUserHandler = async () => {
        // @ts-ignore
        setIsLoading(true)

        const response = await API.post(EXTERNAL_USERS_URL, {
            firstName: uName.current.value,
            email: uEmail.current.value,
            company: uCompany.current.value,
            password: 'INVALID',
            country: country.current.value



        })
        if (response.status === 200) {

            cogoToast.info('Created!', { position: 'top-right' })
            // $('#dataTable').DataTable().ajax.reload();
            getUsersData();
            customSwal2.fire(
                'User Created!',
                '',
                'success'
            )
        } else {
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )
            setIsLoading(false)
        }



        uName.current.value = '';
        uEmail.current.value = '';
        country.current.value = '';
        uCompany.current.value = '';
        setUserShow(false)
        setIsLoading(false)
    }

    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const tableActions = [

        {
            icon: 'password',
            tooltip: 'Reset Password',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                resetPassword(rowData.email)
            }
        },
        {
            icon: 'lock',
            tooltip: 'Reset MFA',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                resetMFA(rowData.email)
            }
        },
        {
            icon: 'delete',
            tooltip: 'Delete User',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                deleteUser(rowData.email)
            }
        },

    ]

    const localization = {
        header: {
            actions: 'Role Assignment'
        }
    };

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        // const filteredData = data.filter(item => {
        //   return (
        //     (locationOneId === '' || item.locationOneId === locationOneId) &&
        //     (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
        //     (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
        //     (locationFourId === '' || item.locationFourId === locationFourId)
        //   );
        // });

        // setFilterData(filteredData);
        // setSelectedLocationOne(locationOneId)
        // setSelectedLocationTwo(locationTwoId)
        // setSelectedLocationThree(locationThreeId)
        // setSelectedLocationFour(locationFourId)
    };

    const updateUserData = async (column, id, value, email) => {

        const response = await API.patch(USERS_URL_WITH_ID(id), { [column]: value })



        if (response.status === 204) {
            cogoToast.success('Updated!')
        }
    }

   

    const availableCountries = Object.entries(roleCountryMap).reduce((acc, [role, country]) => {
        if (roles.some(userRole => userRole.name === role)) {
            acc.push(country);
        }
        return acc;
    }, []);

    return (
        <CardOverlay>

            {progress > 0 && progress < 100 && <LinearProgress className="mb-3" variant="determinate" value={progress} />}

            <button className='btn btn-primary me-2' onClick={() => setUserShow(true)}>Create External User</button>
            <button className='btn btn-secondary' onClick={() => setExcelShow(true)}>Upload Bulk External Users</button>


            <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={userColumns}
                    data={data}
                    title="External User Managment"
                    style={tableStyle}
                    actions={tableActions}
                    options={tableOptions}
                    localization={localization}
                    editable={{
                        onRowUpdate: (newData, oldData) =>
                            new Promise((resolve, reject) => {
                                // Find the index of the row in the data array
                                const dataUpdate = [...data];
                                const index = oldData.tableData.id;
                                dataUpdate[index] = newData;

                                // If type of data has changed update the company
                                if (newData.type !== oldData.type) {
                                    updateUserData('company', oldData.id, newData.type, oldData.email);
                                }

                                // Loop over each field in the newData object to update the user data
                                Object.keys(newData).forEach((field) => {
                                    if (newData[field] !== oldData[field]) {
                                        updateUserData(field, oldData.id, newData[field], oldData.email);
                                    }
                                });

                                // On successful update, change the local state
                                setData(dataUpdate);

                                resolve();
                            }),
                    }}
                />
            </ThemeProvider>

            <Modal
                show={mdShow}
                size="lg"
                onHide={() => setMdShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    Assign Permissions to {selectedUserId.name}
                </Modal.Header>

                <Modal.Body>
                    <form className="forms">

                        {/* <h4>Country Admin</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.country.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.country.includes(i.id)} onChange={(e) => handleRoleChange(e, 'country')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div> */}
                        <AllFilterLocation handleFilter={handleFilter} countryRoles={me?.validationRoles} disableAll={true} period={false} />

                        <h4>EHS Observation</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.ehs.slice() // Create a shallow copy of the array to avoid mutating the original array.
                                    .sort((a, b) => {
                                        // Check if 'View Only' is in the name, and sort accordingly.
                                        const nameA = a.name.toLowerCase();
                                        const nameB = b.name.toLowerCase();
                                        if (nameA.includes('view only') && !nameB.includes('view only')) {
                                            return -1; // 'a' should come before 'b'.
                                        }
                                        if (!nameA.includes('view only') && nameB.includes('view only')) {
                                            return 1; // 'a' should come after 'b'.
                                        }
                                        return 0; // No change in order.
                                    }).map((i, k) => {
                                        return (
                                            <label className='me-3' key={k}>
                                                <input value={i.id} checked={selectedRoles.ehs.includes(i.id)} onChange={(e) => handleRoleChange(e, 'ehs')} type='checkbox' /> {i.name}
                                            </label>
                                        )
                                    })
                            }

                        </div>
                        <h4>ePermit to Work</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.eptw.slice() // Create a shallow copy of the array to avoid mutating the original array.
                                    .sort((a, b) => {
                                        // Check if 'View Only' is in the name, and sort accordingly.
                                        const nameA = a.name.toLowerCase();
                                        const nameB = b.name.toLowerCase();
                                        if (nameA.includes('view only') && !nameB.includes('view only')) {
                                            return -1; // 'a' should come before 'b'.
                                        }
                                        if (!nameA.includes('view only') && nameB.includes('view only')) {
                                            return 1; // 'a' should come after 'b'.
                                        }
                                        return 0; // No change in order.
                                    }).map((i, k) => {
                                        return (
                                            <label className='me-3' key={k}>
                                                <input value={i.id} checked={selectedRoles.eptw.includes(i.id)} onChange={(e) => handleRoleChange(e, 'eptw')} type='checkbox' /> {i.name}
                                            </label>
                                        )
                                    })
                            }

                        </div>
                        <h4>Incident Reporting</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.incident.slice() // Create a shallow copy of the array to avoid mutating the original array.
                                    .sort((a, b) => {
                                        // Check if 'View Only' is in the name, and sort accordingly.
                                        const nameA = a.name.toLowerCase();
                                        const nameB = b.name.toLowerCase();
                                        if (nameA.includes('view only') && !nameB.includes('view only')) {
                                            return -1; // 'a' should come before 'b'.
                                        }
                                        if (!nameA.includes('view only') && nameB.includes('view only')) {
                                            return 1; // 'a' should come after 'b'.
                                        }
                                        return 0; // No change in order.
                                    }).map((i, k) => {
                                        return (
                                            <label className='me-3' key={k}>
                                                <input value={i.id} checked={selectedRoles.incident.includes(i.id)} onChange={(e) => handleRoleChange(e, 'incident')} type='checkbox' /> {i.name}
                                            </label>
                                        )
                                    })
                            }

                        </div>
                        <h4>Inspection and Audit</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.inspection.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.inspection.includes(i.id)} onChange={(e) => handleRoleChange(e, 'inspection')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>Plant and Equipment</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.plant.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.plant.includes(i.id)} onChange={(e) => handleRoleChange(e, 'plant')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <>
                        <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>

                        {selectedUserId.id && <Button variant="primary" onClick={handleAssignSubmit}>Assign</Button>}

                    </>


                </Modal.Footer>
            </Modal>


            <Modal
                show={userShow}
                onHide={() => setUserShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <form className="forms">
                        <div className="form-group">
                            <label htmlFor="user_name" >Name</label>
                            <Form.Control type="text" ref={uName} id="user_name" placeholder="Enter User Name" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_category" >Email</label>
                            <Form.Control type="email" ref={uEmail} id="user_category" placeholder="Enter User Email" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_company" >Company Name</label>
                            <Form.Control type="text" ref={uCompany} id="user_company" placeholder="Enter Company Name" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_country">Country</label>
                            <Form.Control as="select" ref={country} id="user_country" defaultValue="">
                                <option disabled value="">Select a Country</option>
                                {availableCountries.map(country => (
                                    <option key={country} value={country}>{country}</option>
                                ))}
                            </Form.Control>
                        </div>


                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setUserShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={createUserHandler}>Create</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>

            <Modal
                show={excelShow}
                onHide={() => setExcelShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <DropzoneArea
                        acceptedFiles={[
                            'application/vnd.ms-excel',
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        ]}
                        dropzoneText={"Drag and drop an Excel file here or click"}
                        filesLimit={1}
                        onChange={handleFileChange}
                    />
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setExcelShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={uploadBulkUser}>Upload</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>
        </CardOverlay>
    )
}


export default AppUser;

import React, { Component, useState, useEffect, useRef } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { OBSERVATION_REPORT_URL, OBSERVATION_REPORT_WITH_ID, STATIC_URL, USERS_URL } from '../constants';
import { Button } from 'primereact/button';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import ObservationModal from './ObservationModal';
import { observationColumns, tableOptions } from './TableColumns';
import CardOverlay from './CardOverlay';
import FilterLocation from './FilterLocation';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import AllFilterLocation from './AllLocationFilter';
import { Checkbox } from '@mui/material';
import moment from 'moment'
import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import { Badge } from 'react-bootstrap';
import { Button as Button1 } from 'primereact/button';
import DatePicker from 'react-datepicker'
import * as XLSX from 'xlsx';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
FilterService.register('custom_created', (value, filters) => {
  const { startMonth, startYear, endMonth, endYear } = filters;

  if (startMonth === undefined || startYear === undefined || endMonth === undefined || endYear === undefined) {
    // If any of the required filter parameters is missing, return true to include the value
    return true;
  }

  const fromDate = moment(`${startMonth}-${startYear}`, 'MM-YYYY').startOf('month');
  const toDate = moment(`${endMonth}-${endYear}`, 'MM-YYYY').endOf('month');

  const date = moment(value, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

  return date.isBetween(fromDate, toDate, null, '[]');
});
function isBetweenDateRange(dateString, date1, date2) {
  // Parse the date strings using Moment.js
  const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

  // Check if the parsed date is between date1 and date2
  return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
}

const Observation = ({ obsdata, clear, search }) => {
  console.log(obsdata)

  const [globalFilterValue, setGlobalFilterValue] = useState('');
  const dataTableRef = useRef(null);
  const [dates, setDates] = useState(null);
  const [showOverdue, setShowOverdue] = useState(false);
  const [category, setCategory] = useState([{ name: 'Health', value: 'Health' }, { name: 'Safety', value: 'Safety' }, { name: 'Environment', value: 'Environment' }])
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)
  const [Search, setSearch] = useState([])
  const [project, setProject] = useState([])
  const [status, setStatus] = useState([])

  const [filters, setFilters] = useState(null);

  const [dataFilter, setDataFilter] = useState([]);
  let k = 0;

  const history = useHistory();
  const thead = [
    'id',
    'Type',
    'Category',
    'Description',
    'Rectified Status',
    'Reported By',
    'Remarks',
    'Action Taken',


  ];
  const defaultMaterialTheme = createTheme();

  const statuses = [{ name: 'Overdue', value: 'Overdue' }, { name: 'Upcoming', value: 'Upcoming' }, { name: 'Due Soon', value: 'Due Soon' }, { name: 'None', value: 'None' }];
  const getSeverity = (status) => {
    switch (status) {
      case 'Overdue':
        return 'danger';

      case 'Upcoming':
        return 'info';

      case 'Due Soon':
        return 'warning';

      case 'None':
        return null;
    }
  };
  const [data, setData] = useState([]);
  const [filterData, setFilterData] = useState([]);
  const [users, setUsers] = useState([])

  useEffect(() => { getAllUsers() }, [])
  const getAllUsers = async () => {
    const response = await API.get(USERS_URL);
    setUsers(response.data)
  }
  useEffect(() => {
    if (obsdata) {
      getObservationData();

    }
    setData(obsdata)
    setFilterData(obsdata)
    initFilters();

  }, [obsdata])


  useEffect(() => {
    initFilters();
  }, [clear])

  useEffect(() => {

  }, [search])

  const initFilters = () => {
    setFilters({
      global: { value: null, matchMode: FilterMatchMode.CONTAINS },
      'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
      category: { value: null, matchMode: FilterMatchMode.IN },
      type: { value: null, matchMode: FilterMatchMode.IN },
      description: { value: null, matchMode: FilterMatchMode.IN },
      dueDate: { value: null, matchMode: FilterMatchMode.IN },
      color: { value: null, matchMode: FilterMatchMode.IN },
      newStatus: { value: null, matchMode: FilterMatchMode.IN },
      'locationFour.name': { value: null, matchMode: FilterMatchMode.IN },
      created: { value: null, matchMode: FilterMatchMode.CUSTOM },
    });
    setGlobalFilterValue('');
  };
  const getObservationData = async () => {



    const obs = obsdata.map(item => {
      return { name: item.locationFour?.name || '', value: item.locationFour?.name || '' }
    })
    setProject(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    const status = obsdata.map(item => {
      return { name: item.newStatus, value: item.newStatus }
    })
    setStatus(status.filter((ele, ind) => ind === status.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    // const params = {
    //   "include": [{ "relation": "submitted" }]

    // };
    // const response = await API.get(`${OBSERVATION_REPORT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    // if (response.status === 200) {
    //   const preprocessedData = response.data.map(item => ({
    //     ...item,
    //     'submitted.firstName': item.submitted ? item.submitted.firstName : '',
    //     'color': moment().isAfter(moment(item.dueDate, 'DD-MM-YYYY')) ? 'Overdue' : 'Upcoming',
    //     created: moment(item.created).format('Do MMM YYYY hh:mm A') // Ensure this is a string
    //   }));


    // setData(obsdata);
    // setFilterData(obsdata);

    // setTotalObservation(preprocessedData.length)

  }

  const [showReportModal, setShowReportModal] = useState(false);
  const [reportData, setReportData] = useState(null);

  const viewObservationReport = async (id) => {

    const params = {
      "include": [{ "relation": "submitted" }, { "relation": "actions" }, { "relation": "workActivity" }, { "relation": "ghsOne" }, { "relation": "ghsTwo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" },
        { "relation": "hazardCategory" }, { "relation": "hazardDescription" }, { "relation": "hazardType" }]

    };
    const response = await API.get(`${OBSERVATION_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

    if (response.status === 200) {

      const actionUploads = (response.data.actions && response.data.actions.length) ? response.data.actions.flatMap(obj => obj.uploads) : [];

      response.data.uploads = [...response.data.uploads]

      response.data.uploads = response.data.uploads ? response.data.uploads.map(i => {
        return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      }) : []

      response.data.evidences = response.data.evidences ? response.data.evidences.map(i => {
        return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      }) : []

      response.data.evidence = [...response.data.evidence, ...actionUploads]

      response.data.evidence = response.data.evidence ? response.data.evidence.map(i => {
        return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      }) : []
      setReportData(response.data)
      setShowReportModal(true)
    }

  }

  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };

  const tableActions = [
    {
      icon: 'visibility',
      tooltip: 'View Report',
      onClick: (event, rowData) => {
        // Do save operation

        viewObservationReport(rowData.id)
      }
    }
  ]

  const localization = {
    header: {
      actions: 'View'
    }
  };

  // useEffect(()=>{
  //   const filteredData = obsdata.filter(item => {
  //     return (
  //       (locationOneId === '' || item.locationOneId === locationOneId) &&
  //       (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
  //       (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
  //       (locationFourId === '' || item.locationFourId === locationFourId)
  //     );
  //   });

  //   // setFilterData(filteredData);
  //   // setTotalObservation(filterData.length)

  // },[locationOneId,locationTwoId,locationThreeId,locationFourId])



  useEffect(() => {
    let filteredData = obsdata; // Assuming response.data is your fetched data

    if (showOverdue) {
      const currentDate = moment();
      filteredData = filteredData.filter(item => {
        return moment(item.dueDate, 'DD-MM-YYYY').isBefore(currentDate);
      });
    }

    setFilterData(filteredData);
  }, [showOverdue]);
  const onDateSearch = () => {
    const [from, to] = [startDate, endDate];
    if (from === null && to === null) return true;
    if (from !== null && to === null) return true;
    if (from === null && to !== null) return true;
    const start = moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month');
    const end = moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month');

    //  console.log(start,end)
    const searchData = obsdata.filter(item => isBetweenDateRange(item.created, start, end))

    setFilterData(searchData)

    // return isBetweenDateRange(value, moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month'), moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month'))
  }


  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }
  const exportExcel = () => {
    import('xlsx').then((xlsx) => {


      const worksheet = xlsx.utils.json_to_sheet(filterData);
      const workbook = { Sheets: { data: worksheet }, SheetNames: ['data'] };
      const excelBuffer = xlsx.write(workbook, {
        bookType: 'xlsx',
        type: 'array'
      });

      saveAsExcelFile(excelBuffer, 'products');
    });
  };

  const saveAsExcelFile = (buffer, fileName) => {
    import('file-saver').then((module) => {
      if (module && module.default) {
        let EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
        let EXCEL_EXTENSION = '.xlsx';
        const data = new Blob([buffer], {
          type: EXCEL_TYPE
        });

        module.default.saveAs(data, fileName + '_export_' + new Date().getTime() + EXCEL_EXTENSION);
      }
    });
  };
  const getClosedDate = (item) => {

    return moment(item.createdDate).format('MMMM Do YYYY, h:mm:ss a')

  }
  function getName(id) {
    const user = users.find(user => user.id === id)
    return id ? user.firstName : ''
  }
  const getCloseActionDate = (item) => {

    if (item.status === 'At Risk - Closed' || item.status === 'Action Verified - Closed' || item.status === 'Reported & Closed') {
      if (item.actions) {
        const last = item.actions[item.actions.length - 1]

        return moment(last.createdDate).format('Do MMM YYYY')
      }

    } else {
      return ''
    }

  }
  const uploadOBS = (item) => {
    const prefixedUploads = item.map(upload => STATIC_URL + '/' + upload);
    return prefixedUploads.join(',')
  }
  const [dataList, setDataList] = useState([]);
  const exportCSV = () => {


    let data = []
    let filter = []
    if (dataFilter.length !== 0) {
      filter = dataFilter
    } else {
      filter = filterData
    }
    filter.map(item => {

      const obj = {
        ID: item.maskId,
        Description: item.description,
        Status: item.newStatus,
        Domain: item.category,
        Category: item.type,
        ReportedDate: item.created,
        DueDate: item.type !== 'Safe' ? item.rectifiedStatus !== 'Yes' ? item.dueDate : '' : '',
        ActionAssignee: '',
        Reporter: item.submitted.firstName,
        ReporterOrganization: item.submitted.company || '',
        Location: item.locationTwo.name || '' + ' ' + item.locationOne.name || '',
        BusinessUnit: item.locationThree.name || '',
        ProjectDCName: item.locationFour ? item.locationFour.name : '',
        Level: item.locationFive.name || '',
        Zone: item.locationSix.name || '',
        WorkActivity: item.workActivity.name || '',
        GMSStandard: item.ghsOne.name || '',
        SubGMS: item.ghsTwo.name || '',
        ActionTaken: item.rectifiedStatus === 'Yes' ? item.actionTaken : '',
        Evidences: item.rectifiedStatus === 'Yes' ? item.evidence.join() : '',
        ClosedDate: getCloseActionDate(item),
        Upload: uploadOBS(item.uploads)
      }

      if (item.uploads.length !== 0) {
        obj[`Upload`] = item.uploads.join()

      }
      if (item.type !== 'Safe' && item.rectifiedStatus !== 'Yes' && item.actions && item.actions.length > 0) {

        item.actions.forEach((action, i) => {
          if (action.actionType === 'action_owner') {
            k++;
            if (k === 1) {
              obj[`AssignedAction-A${k}`] = action.actionToBeTaken;
              if (action.status === 'open') {
                obj[`ActionAssignee`] = getName(action.assignedToId);
              }


            } else {
              obj[`ActionReviewerComments&ReassignedAction-A${k}`] = action.comments;
              if (action.status === 'open') {
                obj[`ActionAssignee`] = getName(action.assignedToId);
              }


            }
            if (action.status === 'submitted' && action.uploads.length !== 0) {
              obj[`Evidence`] = uploadOBS(action.uploads)
            }
          } else if (action.actionType === 'reject' && action.status === 'submitted') {
            obj[`ActionReviewedBy`] = getName(action.assignedToId);
            obj[`Date`] = moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a');
          } else if (action.actionType === 'approve' && action.status === 'submitted') {
            obj[`ActionReviewedBy`] = getName(item.reviewerId);
            obj[`Date`] = moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a');
            obj[`ActionReviewerComments`] = action.comments;
          }
        });
        k = 0;
      }

      // if (item.rectifiedStatus === 'Yes') {
      //   obj[`ActionTaken`] = item.actionTaken
      //   if (item.evidence.length !== 0) {
      //     obj[`Evidence`] = item.evidence.join();

      //   }
      // }


      data = [...data, obj];

    })


    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, `${'Observation' + moment().format('DD-MM-YYYY HH:MM')}.xlsx`);

  }
  const renderHeader = () => {


    return (
      <div className='d-flex justify-content-end align-items-end'>
        <Button label="Export" icon="pi pi-upload" className="p-button" onClick={exportCSV} />

      </div>
    );
  };

  const header = renderHeader();
  const onGlobalFilterChange = (e) => {
    const value = e.target.value;
    let _filters = { ...filters };

    _filters['global'].value = value;

    setFilters(_filters);
    setGlobalFilterValue(value);
  };

  // const statusBodyTemplate = (row) => {

  //   return (
  //     <div className='maskid' >
  //       {row.color ==='Overdue' ? <span className='overdue mt-2' ></span> : ''}
  //     </div>
  //   )
  // }
  const statusBodyTemplate = (rowData) => {
    return <Badge pill bg={getSeverity(rowData.color)} >{rowData.color}</Badge>
  };
  const maskIdBodyTemplate = (row) => {

    return (
      <div className='maskid' onClick={() => viewObservationReport(row.id)}>
        {row.maskId}
      </div>
    );

  }
  const dateBodyTemplate = (row) => {

    return (<>{row.created}</>)

  }
  const viewBodyTemplate = (row) => {
    return (
      <div className="table-action d-flex ">
        <i className="mdi mdi-eye" onClick={() => viewObservationReport(row.id)}></i>

      </div>
    )
  }

  const typeBodyTemplate =(row)=>{
    if(row.isQR){
      return row.type +' ('+row.conditionAct+')'
    }else{
      return row.type
    }
  }
  const categoryFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={category} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const typeFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={[{ name: 'At Risk', value: 'At Risk' },{ name: 'Safe', value: 'Safe' },{ name: 'At Risk(Act)', value: 'At Risk(Act)' },{ name: 'At Risk(Condition)', value: 'At Risk(Condition)' }, { name: 'Safe(Act)', value: 'Safe(Act)' },{ name: 'Safe(Condition)', value: 'Safe(Condition)' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const projectFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={project} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }

  const cstatusFilterTemplate = (options) => {
    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={status} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }

  const representativesItemTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.value}</span>
      </div>
    );
  };

  const dueDateTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.dueDate
          ? moment(option.dueDate, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
          : '-'}</span>
      </div>
    );
  };

  const statusFilterTemplate = (options) => {
    return <React.Fragment>
      <div className="mb-3 font-bold">Type</div>
      <MultiSelect value={options.value} options={statuses} itemTemplate={statusItemTemplate} onChange={(e) => options.filterCallback(e.value, options.index)} optionLabel="name" placeholder="Any" className="p-column-filter" />
    </React.Fragment>
  };
  const statusItemTemplate = (option) => {
    return <Badge pill bg={getSeverity(option.value)}>{option.name}</Badge>
  };
  // const bodyFilterTemplate = (row) => {
  //   console.log(row)
  //   return <>{JSON.parse(row.remarks).location4}</>
  // }

  const monthFilterTemplate = (options) => {
    const [from, to] = options.value ?? [null, null];
    console.log(options)
    return (
      <div className="d-flex ">


        <Calendar value={from} view='month' dateFormat="mm/yy" onChange={(e) => options.filterApplyCallback([e.value, to])} className="w-full" placeholder="From" showIcon />
        <Calendar value={to} view='month' dateFormat="mm/yy" onChange={(e) => options.filterApplyCallback([from, e.value])} className="w-full" placeholder="To" showIcon />
      </div>
    )
  }


  const sortDate = (e) => {

    if (e.order === 1) {
      return e.data.sort((a, b) => {

        // Parse the dates using Moment.js
        const dateA = moment(a.created, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);
        const dateB = moment(b.created, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);

        // Compare the dates
        if (dateA.isBefore(dateB)) {
          return -1; // dateA comes before dateB
        } else if (dateA.isAfter(dateB)) {
          return 1; // dateA comes after dateB
        } else {
          return 0; // dates are equal
        }
      });
    } else {

      return e.data.sort((a, b) => {
        // Parse the dates using Moment.js
        const dateA = moment(a.created, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A']);
        const dateB = moment(b.created, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A']);

        // Compare the dates
        if (dateA.isBefore(dateB)) {
          return -1; // dateA comes before dateB
        } else if (dateA.isAfter(dateB)) {
          return 1; // dateA comes after dateB
        } else {
          return 0; // dates are equal
        }
      }).reverse()
    }
  }



  return (
    <>

      {/* <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} /> */}
      {/* <Checkbox
        checked={showOverdue}
        onChange={e => setShowOverdue(e.target.checked)}
        color="primary"
      />
      Show Overdue */}
      {/* <ThemeProvider theme={defaultMaterialTheme}>
        <MaterialTable
          columns={observationColumns}
          data={filterData}
          title="EHS Observation Report"
          style={tableStyle}
          actions={tableActions}
          options={{ pageSize: 20, exportButton: true, ...tableOptions }}
          localization={localization}
        />
      </ThemeProvider> */}
      <DataTable value={filterData} onValueChange={filteredData => setDataFilter(filteredData)} paginator rows={10} ref={dataTableRef} header={header} filters={filters} globalFilterFields={["maskId"]} onFilter={(e) => { console.log(e); setFilters(e.filters) }} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        rowsPerPageOptions={[10, 25, 50]}
        emptyMessage="No Data found." >

        <Column field='color' header='Status' body={statusBodyTemplate} filter filterElement={statusFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field='maskId' body={maskIdBodyTemplate} header="Observation ID" headerStyle={{ width: '15%' }} sortable></Column>

        <Column field='created' body={dateBodyTemplate} header="Reported On" sortable headerStyle={{ width: '10%' }} sortFunction={sortDate}></Column>

        <Column field="category" header="Domain" filter filterElement={categoryFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field="type" header="Category"  filter filterElement={typeFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field="newStatus" header="Current Status" filter filterElement={cstatusFilterTemplate} showFilterMatchModes={false}></Column>


        <Column field="locationFour.name" header="Project/DC name" filter filterElement={projectFilterTemplate} showFilterMatchModes={false}></Column>
        <Column field="actionOwner.firstName" header="Action Assignee"  showFilterMatchModes={false}></Column>
        <Column field="dueDate" body={dueDateTemplate} header="Action Due" ></Column>

        <Column field="closeDate" header="Action Closed" ></Column>

      </DataTable>

      <ObservationModal reportData={reportData} showReportModal={showReportModal} setShowReportModal={(status) => setShowReportModal(status)} />



    </>
  )
}

export default Observation;

import React, { Component, useState, useEffect, useRef } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { OBSERVATION_REPORT_BY_OTHERS_URL, OBSERVATION_REPORT_URL, USERS_URL, OBSERVATION_REPORT_WITH_ID, STATIC_URL } from '../constants';
import { Button } from 'primereact/button';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import ObservationModal from './ObservationModal';
import { observationColumns, tableOptions } from './TableColumns';
import CardOverlay from './CardOverlay';
import FilterLocation from './FilterLocation';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import AllFilterLocation from './AllLocationFilter';
import { Checkbox } from '@mui/material';
import moment from 'moment'
import { Badge } from 'react-bootstrap';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import { Calendar } from 'primereact/calendar';
import { Button as Button1 } from 'primereact/button';
import * as XLSX from 'xlsx';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const ObservationOther = ({ setTotalOtherObservation, obsdata, clear, search }) => {
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)
  const [showOverdue, setShowOverdue] = useState(false);
  const [project, setProject] = useState([])
  const [status, setStatus] = useState([])
  const [filters, setFilters] = useState(null);
  const history = useHistory();
  const thead = [
    'id',
    'Type',
    'Category',
    'Description',
    'Rectified Status',

    'Remarks',
    'Action Taken',


  ];
  const defaultMaterialTheme = createTheme();
  const dataTableRef = useRef();
  const [dataFilter, setDataFilter] = useState([]);
  let k = 0;
  const [data, setData] = useState(obsdata);
  const [filterData, setFilterData] = useState(obsdata);
  const [users, setUsers] = useState([]);
  const [users1, setUsers1] = useState([]);
  const [globalFilterValue, setGlobalFilterValue] = useState('');
  const [category, setCategory] = useState([{ name: 'Health', value: 'Health' }, { name: 'Safety', value: 'Safety' }, { name: 'Environment', value: 'Environment' }])
  useEffect(() => {
    // getObservationData();
    if (obsdata) {
      getObservationData();

    }
    setData(obsdata)
    setFilterData(obsdata)
    initFilters()

  }, [obsdata])

  useEffect(() => {
    initFilters();
  }, [clear])

  // useEffect(() => {
  //   onGlobalFilterChange(search);
  // }, [search])
  useEffect(() => { getAllUsers() }, [])
  const getAllUsers = async () => {
    const response = await API.get(USERS_URL);
    setUsers1(response.data)
  }
  const initFilters = () => {
    setFilters({
      global: { value: null, matchMode: FilterMatchMode.CONTAINS },
      'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
      category: { value: null, matchMode: FilterMatchMode.IN },
      type: { value: null, matchMode: FilterMatchMode.IN },
      description: { value: null, matchMode: FilterMatchMode.IN },
      dueDate: { value: null, matchMode: FilterMatchMode.IN },
      color: { value: null, matchMode: FilterMatchMode.IN },
      newStatus: { value: null, matchMode: FilterMatchMode.IN },
      'locationFour.name': { value: null, matchMode: FilterMatchMode.IN },
      'submitted.firstName': { value: null, matchMode: FilterMatchMode.IN },
      created: { value: null, matchMode: FilterMatchMode.CUSTOM },
    })
    setGlobalFilterValue('');
  }
  function getName(id) {
    const user = users1.find(user => user.id === id)
    return id ? user.firstName : ''
  }
  const getCloseActionDate = (item) => {

    if (item.status === 'At Risk - Closed' || item.status === 'Action Verified - Closed' || item.status === 'Reported & Closed') {
      if (item.actions) {
        const last = item.actions[item.actions.length - 1]
        console.log(last)

        return moment(last.createdDate).format('Do MMM YYYY')
      }

    } else {
      return ''
    }

  }
  const onDateSearch = () => {
    const [from, to] = [startDate, endDate];
    if (from === null && to === null) return true;
    if (from !== null && to === null) return true;
    if (from === null && to !== null) return true;
    const start = moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month');
    const end = moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month');

    //  console.log(start,end)
    const searchData = data.filter(item => isBetweenDateRange(item.created, start, end))

    setFilterData(searchData)

    // return isBetweenDateRange(value, moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month'), moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month'))
  }
  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }

  const getObservationData = async () => {

    const obs = obsdata.map(item => {
      return { name: item.locationFour?.name || '', value: item.locationFour?.name || '' }
    })
    setProject(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    const status = obsdata.map(item => {
      return { name: item.newStatus, value: item.newStatus }
    })
    setStatus(status.filter((ele, ind) => ind === status.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    const user = obsdata.map(item => {
      return { name: item.submitted?.firstName, value: item.submitted?.firstName }
    })
    setUsers(user.filter((ele, ind) => ind === user.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    // const params = {
    //   "include": [{ "relation": "submitted" }]

    // };
    // const response = await API.get(`${OBSERVATION_REPORT_BY_OTHERS_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    // if (response.status === 200) {

    //   const preprocessedData = response.data.map(item => ({
    //     ...item,
    //     'submitted.firstName': item.submitted ? item.submitted.firstName : '',
    //     'color': moment().isAfter(moment(item.dueDate, 'DD-MM-YYYY')) ? 'Overdue' : 'Upcoming',
    //     'created': moment(item.created).format('Do MMM YYYY hh:mm A') // Ensure this is a string
    //   }));
    //   setData(preprocessedData)
    //   setFilterData(preprocessedData)
    //   setTotalOtherObservation(preprocessedData.length)
    // }

  }

  const [showReportModal, setShowReportModal] = useState(false);
  const [reportData, setReportData] = useState(null);

  const viewObservationReport = async (id) => {
    try {
      const params = {
        "include": [
          { "relation": "actions" },
          { "relation": "workActivity" },
          { "relation": "ghsOne" },
          { "relation": "ghsTwo" },
          { "relation": "locationOne" },
          { "relation": "locationTwo" },
          { "relation": "locationThree" },
          { "relation": "locationFour" },
          { "relation": "locationFive" },
          { "relation": "locationSix" },
          // { "relation": "hazardCategory" }, { "relation": "hazardDescription" }, { "relation": "hazardType" }
        ]
      };
      const response = await API.get(`${OBSERVATION_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

      if (response.status === 200) {
        const actionUploads = response.data.actions?.flatMap(obj => obj.uploads) || [];

        const formatImages = (images) => images?.map(i => ({
          src: `${STATIC_URL}/${i}`,
          width: 4,
          height: 3
        })) || [];

        response.data.uploads = formatImages(response.data.uploads);
        if (response.data.evidences) {
          response.data.evidences = formatImages(response.data.evidences);
        }

        response.data.evidence = formatImages([
          ...(response.data.evidence || []),
          ...actionUploads
        ]);

        setReportData(response.data);
        setShowReportModal(true);
      }
    } catch (error) {
      console.error("Failed to fetch observation report:", error);
    }
  };


  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };

  const tableActions = [
    {
      icon: 'visibility',
      tooltip: 'View Report',
      onClick: (event, rowData) => {
        // Do save operation

        viewObservationReport(rowData.id)
      }
    }
  ]
  const viewBodyTemplate = (row) => {
    return (
      <div className="table-action d-flex ">
        <i className="mdi mdi-eye" onClick={() => viewObservationReport(row.id)}></i>

      </div>
    )
  }
  const localization = {
    header: {
      actions: 'View'
    }
  };
  // useEffect(()=>{
  //   const filteredData = data.filter(item => {
  //     return (
  //       (locationOneId === '' || item.locationOneId === locationOneId) &&
  //       (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
  //       (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
  //       (locationFourId === '' || item.locationFourId === locationFourId)
  //     );
  //   });

  //   setFilterData(filteredData);
  //   setTotalOtherObservation(filterData.length)

  // },[locationOneId,locationTwoId,locationThreeId,locationFourId])


  // const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
  //   const filteredData = data.filter(item => {
  //     return (
  //       (locationOneId === '' || item.locationOneId === locationOneId) &&
  //       (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
  //       (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
  //       (locationFourId === '' || item.locationFourId === locationFourId)
  //     );
  //   });

  //   setFilterData(filteredData);
  // };
  useEffect(() => {
    let filteredData = data; // Assuming response.data is your fetched data

    if (showOverdue) {
      const currentDate = moment();
      filteredData = filteredData.filter(item => {
        return moment(item.dueDate, 'DD-MM-YYYY').isBefore(currentDate);
      });
    }

    setFilterData(filteredData);
  }, [showOverdue]);

  const uploadOBS = (item) => {
    const prefixedUploads = item.map(upload => STATIC_URL + '/' + upload);
    return prefixedUploads.join(',')
  }
  const exportCSV = () => {



    let data = []

    let filter = []
    if (dataFilter.length !== 0) {
      filter = dataFilter
    } else {
      filter = filterData
    }

    filter.map(item => {

      const obj = {
        ID: item.maskId,
        Description: item.description,
        Status: item.newStatus,
        Domain: item.category,
        Category: item.type,
        ReportedDate: item.created,
        DueDate: item.type !== 'Safe' ? item.rectifiedStatus !== 'Yes' ? item.dueDate : '' : '',
        ActionAssignee: '',
        Reporter: item.submitted?.firstName,
        ReporterOrganization: item.submitted?.company || '',
        Location: item.locationTwo?.name || '' + ' ' + item.locationOne?.name || '',
        BusinessUnit: item.locationThree?.name || '',
        ProjectDCName: item.locationFour ? item.locationFour.name : '',
        Level: item.locationFive?.name || '',
        Zone: item.locationSix?.name || '',
        WorkActivity: item.workActivity?.name || '',
        GMSStandard: item.ghsOne?.name || '',
        SubGMS: item.ghsTwo?.name || '',
        ActionTaken: item.rectifiedStatus === 'Yes' ? item.actionTaken : '',
        Evidences: item.rectifiedStatus === 'Yes' ? item.evidence.join() : '',
        ClosedDate: getCloseActionDate(item),
        Upload: uploadOBS(item.uploads)
      }


      if (item.type !== 'Safe' && item.rectifiedStatus !== 'Yes' && item.actions && item.actions.length > 0) {

        item.actions.forEach((action, i) => {
          if (action.actionType === 'action_owner') {
            k++;
            if (k === 1) {
              obj[`AssignedAction-A${k}`] = action.actionToBeTaken;
              if (action.status === 'open') {
                obj[`ActionAssignee`] = getName(action.assignedToId);
              }

            } else {
              obj[`ActionReviewerComments&ReassignedAction-A${k}`] = action.comments;
              if (action.status === 'open') {
                obj[`ActionAssignee`] = getName(action.assignedToId);
              }

            }
            if (action.status === 'submitted' && action.uploads.length !== 0) {
              obj[`Evidence`] = uploadOBS(action.uploads)
            }
          } else if (action.actionType === 'reject' && action.status === 'submitted') {
            obj[`ActionReviewedBy`] = getName(action.assignedToId);
            obj[`Date`] = moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a');
          } else if (action.actionType === 'approve' && action.status === 'submitted') {
            obj[`ActionReviewedBy`] = getName(item.reviewerId);
            obj[`Date`] = moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a');
            obj[`ActionReviewerComments`] = action.comments;
          }
        });
        k = 0;
      }

      // if (item.rectifiedStatus === 'Yes') {
      //   obj[`ActionTaken`] = item.actionTaken
      //   if (item.evidence.length !== 0) {
      //     obj[`Evidence`] = item.evidence.join();

      //   }
      // }


      data = [...data, obj];

    })
    //   const hyperlinkStyle = {
    //     target: '_blank',
    //     rel: 'noopener noreferrer',
    //     tooltip: 'Click to visit link',
    //     display: 'Hyperlink'
    // };

    const ws = XLSX.utils.json_to_sheet(data);

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, `${'ObservationReportedByOthers' + moment().format('DD-MM-YYYY HH:MM')}.xlsx`);



  }
  const renderHeader = () => {
    // const value = filters['global'] ? filters['global'].value : '';

    return (
      <div className='d-flex justify-content-end align-items-end'>
        <Button label="Export" icon="pi pi-upload" className="p-button" onClick={exportCSV} />


        {/* <div className="">
          <span className='me-3'>Month Filter :</span>
          <Calendar view='month' className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="mm/yy" showIcon />
          <Calendar view='month' className="w-full  me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="mm/yy" showIcon />

          <Button1  className='me-3' rounded text raised severity="success" aria-label="Search" onClick={() => onDateSearch()} label='Apply'/>
          <Button1  rounded text raised severity="danger" aria-label="Cancel" label='Clear All Filters' onClick={() => { setFilterData(data);initFilters(); setStartDate(null); setEndDate(null) }} />

        </div> */}
        {/* <h5 className='m-0'> A listing of all observations reported on the platform for the selected location(s) and time frame.</h5> */}
        {/* <span className="p-input-icon-left">
          <i className="fa fa-search" />
          <InputText type="search" value={globalFilterValue} onChange={(e) => onGlobalFilterChange(e)} />
        </span> */}
      </div>
    );
  };

  const header = renderHeader();
  const onGlobalFilterChange = (event) => {
    const value = event.target.value;
    let _filters = { ...filters };

    _filters['global'].value = value;

    setFilters(_filters);
    setGlobalFilterValue(value);
  };
  const maskIdBodyTemplate = (row) => {

    return (
      <div className='maskid' onClick={() => viewObservationReport(row.id)}>
        {row.maskId}
      </div>
    );

  }
  const dateBodyTemplate = (row) => {

    return (<>{row.created}</>)

  }
  const statusBodyTemplate = (rowData) => {
    return <Badge pill bg={getSeverity(rowData.color)} >{rowData.color}</Badge>
  };

  const categoryFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={category} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const typeFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={[{ name: 'At Risk', value: 'At Risk' }, { name: 'Safe', value: 'Safe' }, { name: 'At Risk(Act)', value: 'At Risk(Act)' }, { name: 'At Risk(Condition)', value: 'At Risk(Condition)' }, { name: 'Safe(Act)', value: 'Safe(Act)' }, { name: 'Safe(Condition)', value: 'Safe(Condition)' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const representativesItemTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.value}</span>
      </div>
    );
  };

  const dueDateTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">
        <span>
          {option.dueDate
            ? moment(option.dueDate, [
                "DD-MM-YYYY",
                "DD/MM/YYYY",
                moment.ISO_8601,
              ]).isValid()
              ? moment(option.dueDate, [
                  "DD-MM-YYYY",
                  "DD/MM/YYYY",
                  moment.ISO_8601,
                ]).format("Do MMM YYYY")
              : "-"
            : "-"}
        </span>
      </div>
    );
  };
  

  const statusFilterTemplate = (options) => {
    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={statuses} itemTemplate={statusItemTemplate} onChange={(e) => options.filterCallback(e.value, options.index)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    )
    // <Dropdown value={options.value} options={statuses} onChange={(e) => options.filterCallback(e.value, options.index)} itemTemplate={statusItemTemplate} placeholder="Select One" className="p-column-filter" showClear />;
  };
  const statusItemTemplate = (option) => {
    return <Badge pill bg={getSeverity(option.value)}>{option.name}</Badge>
  };
  const statuses = [{ name: 'Overdue', value: 'Overdue' }, { name: 'Upcoming', value: 'Upcoming' }, { name: 'Due Soon', value: 'Due Soon' }, { name: 'None', value: 'None' }];
  const getSeverity = (status) => {
    switch (status) {
      case 'Overdue':
        return 'danger';

      case 'Upcoming':
        return 'info';

      case 'Due Soon':
        return 'warning';


      case 'None':
        return null;
    }
  };
  const projectFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={project} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }

  const sortDate = (e) => {
    console.log(e)
    if (e.order === 1) {
      return e.data.sort((a, b) => {
        // Parse the dates using Moment.js
        const dateA = moment(a.created, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);
        const dateB = moment(b.created, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);

        // Compare the dates
        if (dateA.isBefore(dateB)) {
          return -1; // dateA comes before dateB
        } else if (dateA.isAfter(dateB)) {
          return 1; // dateA comes after dateB
        } else {
          return 0; // dates are equal
        }
      });
    } else {

      return e.data.sort((a, b) => {
        // Parse the dates using Moment.js
        const dateA = moment(a.created, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A']);
        const dateB = moment(b.created, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A']);

        // Compare the dates
        if (dateA.isBefore(dateB)) {
          return -1; // dateA comes before dateB
        } else if (dateA.isAfter(dateB)) {
          return 1; // dateA comes after dateB
        } else {
          return 0; // dates are equal
        }
      }).reverse()
    }
  }
  const cstatusFilterTemplate = (options) => {
    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={status} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const reportFilterTemplate = (options) => {
    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={users} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }

  const typeBodyTemplate =(row)=>{
    if(row.isQR){
      return row.type +' ('+row.conditionAct+')'
    }else{
      return row.type
    }
  }
  return (
    <>

      {/* <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} /> */}
      {/* <Checkbox
        checked={showOverdue}
        onChange={e => setShowOverdue(e.target.checked)}
        color="primary"
      />
      Show Overdue */}
      {/* <ThemeProvider theme={defaultMaterialTheme}>
        <MaterialTable
          columns={observationColumns}
          data={filterData}
          title="EHS Observation Reported By Others"
          style={tableStyle}
          actions={tableActions}
          options={{ pageSize: 20, exportButton: true, ...tableOptions }}
          localization={localization}
        />
      </ThemeProvider> */}
      <DataTable value={filterData} paginator rows={10} onValueChange={filteredData => setDataFilter(filteredData)} ref={dataTableRef} header={header} filters={filters} onFilter={(e) => { console.log(e); setFilters(e.filters) }} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        rowsPerPageOptions={[10, 25, 50]}
        emptyMessage="No Data found." >

        {/* <Column body={viewBodyTemplate} header="Action" ></Column> */}
        <Column field='color' header='Timeline' body={statusBodyTemplate} filter filterElement={statusFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field='maskId' body={maskIdBodyTemplate} header="Observation ID" headerStyle={{ width: '15%' }} sortable></Column>

        <Column field='created' body={dateBodyTemplate} header="Reported On" sortable headerStyle={{ width: '16%' }} sortFunction={sortDate}></Column>

        {/* <Column field="submitted.firstName" header="Reported By" sortable  ></Column> */}

        <Column field="category" header="Domain" filter filterElement={categoryFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field="type" header="Category"  filter filterElement={typeFilterTemplate} showFilterMatchModes={false}></Column>

        {/* <Column field="description" header="Brief Description" sortable  ></Column> */}

        <Column field="newStatus" header="Current Status" filter filterElement={cstatusFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field="submitted.firstName" header="Reported By" filter filterElement={reportFilterTemplate} showFilterMatchModes={false}></Column>

        {/* <Column field="status" header="Status" sortable  ></Column>  */}

        <Column field="locationFour.name" header="Project/DC name" filter filterElement={projectFilterTemplate} showFilterMatchModes={false}></Column>
        <Column field="actionOwner.firstName" header="Action Assignee" showFilterMatchModes={false}></Column>
        <Column field="dueDate" body={dueDateTemplate} header="Action Due" ></Column>

        <Column field="closeDate" header="Action Closed"  ></Column>

      </DataTable>

      <ObservationModal reportData={reportData} showReportModal={showReportModal} setShowReportModal={(status) => setShowReportModal(status)} />



    </>
  )
}

export default ObservationOther;

import React from "react";
import { useHistory } from "react-router-dom";

const SubmitCard = (props) => {


    const history = useHistory();
    return (
        <>
            <div className="list align-items-center border-bottom p-2 my-2">
                <div className="wrapper w-100">
                    <h5 className="mb-2 font-weight-medium">
                        {props.data.system.title}
                    </h5>
                    <p className="mb-2 font-weight-medium">
                        Location: {props.data.testCase.location.location}
                    </p>
                    <p className="mb-2 font-weight-medium">
                        Model Number: {props.data.system.part_id}
                    </p>

                    <p className="mb-2 font-weight-medium">
                        Serial / Asset Number: {props.data.system.equip_id}
                    </p>

                    <p className="mb-2 font-weight-medium">
                        Task: {props.data.testCase.name}
                    </p>
                    <div className="d-flex mt-2 mb-2">
                        <div className="text-small me-3 position-absolute-top-right">
                        <span onClick={() => history.push('/response/' + props.data.id)} className="badge badge-opacity-secondary me-2 cursor-pointer"> <i className="mdi mdi-eye"></i> View Response </span>
                            <span className="badge badge-opacity-success me-3">Submitted By {props.data.user.username} </span>
                        </div>


                    </div>


                </div>
            </div>
        </>
    );
}

export default SubmitCard;
import React, { useEffect, useState } from 'react'

import API from '../services/API';

import { useHistory } from "react-router-dom";
import { REPORT_INCIDENT_REVIEW_URL } from '../constants';


import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';

import AllFilterLocation from './AllLocationFilter';

import IncidentInformationModal from './IncidentInformationModal';
import moment from 'moment';

import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import { Button as Button1 } from 'primereact/button';


const IncidentReview = ({ incident, getIncidentData }) => {
  const history = useHistory();

  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)

  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    'maskId': { value: null, matchMode: FilterMatchMode.IN },
    IncidentCategory: { value: null, matchMode: FilterMatchMode.IN },
    status: { value: null, matchMode: FilterMatchMode.IN },
    description: { value: null, matchMode: FilterMatchMode.IN },
    'user.firstName': { value: null, matchMode: FilterMatchMode.IN },
    color: { value: null, matchMode: FilterMatchMode.EQUALS },
    created: { value: null, matchMode: FilterMatchMode.CUSTOM },
  });

  
  const [data, setData] = useState([]);
  const [filterData, setFilterData] = useState([]);
  const [showModal, setShowModal] = useState(false)
  const [user, setUser] = useState(false)

  useEffect(() => {
    getIncidentData();
  }, [showModal])

  useEffect(() => {
    const obs = incident.map(item => {
      return { name: item.user.firstName, value: item.user.firstName }
    })
    setUser(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
    // Filter incidents to only those with status === 'Reported'
    const reportedIncidents = incident.filter(item => item.status === 'Reported');
    setData(reportedIncidents)
    setFilterData(reportedIncidents)
  }, [incident])



  const [currentIncident, setCurrentIncident] = useState('')
  const viewIncident = async (id) => {
    setCurrentIncident(id);
    setShowModal(true)
  }




  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
    const filteredData = data.filter(item => {
      return (
        (locationOneId === '' || item.locationOneId === locationOneId) &&
        (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
        (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
        (locationFourId === '' || item.locationFourId === locationFourId)
      );
    });

    setFilterData(filteredData);
  };


  const renderHeader = () => {
    const value = filters['global'] ? filters['global'].value : '';

    return (
      <div className='d-flex justify-content-between'>
        {/* <div className="">
          <span className='me-3'>Month Filter :</span>
          <Calendar view='month' className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="mm/yy" showIcon />
          <Calendar view='month' className="w-full  me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="mm/yy" showIcon />

          <Button1  className='me-3' rounded text raised severity="success" aria-label="Search" onClick={() => onDateSearch()} label='Apply'/>
          <Button1  rounded text raised severity="danger" aria-label="Cancel" label='Cancel' onClick={() => { setFilterData(data); setStartDate(null); setEndDate(null) }} />

        </div>
        <span className="p-input-icon-left">
          <i className="fa fa-search" />
          <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
        </span> */}
      </div>
    );
  };

  const header = renderHeader();
  const onGlobalFilterChange = (event) => {
    const value = event.target.value;
    let _filters = { ...filters };

    _filters['global'].value = value;

    setFilters(_filters);
  };


  const onDateSearch = () => {
    const [from, to] = [startDate, endDate];
    if (from === null && to === null) return true;
    if (from !== null && to === null) return true;
    if (from === null && to !== null) return true;
    const start = moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month');
    const end = moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month');

    //  console.log(start,end)
    const searchData = data.filter(item => isBetweenDateRange(item.created, start, end))

    setFilterData(searchData)

    // return isBetweenDateRange(value, moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month'), moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month'))
  }
  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }
  const maskIdBodyTemplate = (row) => {

    return (
      <div className='maskid' onClick={() => viewIncident(row.id)}>
        {row.maskId}
      </div>
    );

  }
  const categoryFilterTemplate = (options) => {

    return (
      <React.Fragment>
        
        <MultiSelect value={options.value} options={[{ name: 'Safety', value: 'Safety' }, { name: 'Health', value: 'Health' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const statusFilterTemplate = (options) => {

    return (
      <React.Fragment>
        
        <MultiSelect value={options.value} options={[{ name: 'Reviewed', value: 'Reviewed' }, { name: 'Investigated', value: 'Investigated' }, { name: 'Under Investigation', value: 'Under Investigation' }, { name: 'Reported', value: 'Reported' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const reportFilterTemplate = (options) => {

    return (
        <React.Fragment>
            
            <MultiSelect value={options.value} options={user} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
        </React.Fragment>
    );
}
  const representativesItemTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.value}</span>
      </div>
    );
  };

  const sortDate = (e) => {
    console.log(e)
    if (e.order === 1) {
        return e.data.sort((a, b) => {
            // Parse the dates using Moment.js
            const dateA = moment(a.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ','Do MMM YYYY hh:mm A','Do MMM YYYY', moment.ISO_8601]);
            const dateB = moment(b.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ','Do MMM YYYY hh:mm A','Do MMM YYYY', moment.ISO_8601]);

            // Compare the dates
            if (dateA.isBefore(dateB)) {
                return -1; // dateA comes before dateB
            } else if (dateA.isAfter(dateB)) {
                return 1; // dateA comes after dateB
            } else {
                return 0; // dates are equal
            }
        });
    } else {
      
        return e.data.sort((a, b) => {
            // Parse the dates using Moment.js
            const dateA = moment(a.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ','Do MMM YYYY hh:mm A','Do MMM YYYY',]);
            const dateB = moment(b.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ','Do MMM YYYY hh:mm A','Do MMM YYYY',]);
           
            // Compare the dates
            if (dateA.isBefore(dateB)) {
                return -1; // dateA comes before dateB
            } else if (dateA.isAfter(dateB)) {
                return 1; // dateA comes after dateB
            } else {
                return 0; // dates are equal
            }
        }).reverse()
    }
}
  return (
    <>

      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="">

                {/* <h4 className="card-title">Reported Incident</h4> */}
                <div className="row">
                  <div className="col-12">
                    <div>
                      {/* <button type="button" className="btn btn-light btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setMdShow(true); }}><i className="mdi mdi-account-plus mr-2" /> Create New User</button> */}
                      {/* <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} /> */}
                      {/* <ThemeProvider theme={defaultMaterialTheme}>
                        <MaterialTable
                          columns={incidentColumns}
                          data={filterData}
                          title="Incident Reports"
                          style={tableStyle}
                          actions={tableActions}
                          options={tableOptions}


                        />
                      </ThemeProvider> */}

                      <DataTable value={filterData} paginator rows={10} header={header} filters={filters} onFilter={(e) => { setFilters(e.filters) }} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        rowsPerPageOptions={[10, 25, 50]}
                        emptyMessage="No Data found." >

                        <Column field='maskId' body={maskIdBodyTemplate} header="ID" sortable showFilterMatchModes={false}></Column>

                        <Column field='incidentDate' header="Incident Date" sortable showFilterMatchModes={false} sortFunction={sortDate}></Column>

                        <Column field='title' header="Incident Title" showFilterMatchModes={false}></Column>

                        <Column field='IncidentCategory' header="Category" filter filterElement={categoryFilterTemplate} showFilterMatchModes={false}></Column>

                        <Column field='actualImpact' header="Impact Classification" ></Column>

                      
                        <Column field='user.firstName' header="Reported By" filter filterElement={reportFilterTemplate} showFilterMatchModes={false}></Column>

                        <Column field='reviewer.firstName' header="Reviewer" body={row => row.reviewer && row.reviewer.firstName ? row.reviewer.firstName : ''} showFilterMatchModes={false}></Column>
                        {/* <Column field='incidentOwner.firstName' header="Incident Owner"></Column> */}
                        {/* 
                        <Column header="Actions Taken" body={actionBodyTemplate} headerStyle={{ width: '15%' }}></Column>
                        {isIncidentTrigger &&
                          <Column header="Investigation Status" body={investBodyTemplate} sortable headerStyle={{ width: '16%' }}></Column>
                        } */}

                      </DataTable>

                      {/* <DataTables thead={thead} options={options} /> */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {(currentIncident && showModal) && <IncidentInformationModal readOnly={true} type={'ReadOnly'} id={currentIncident} showModal={showModal} setShowModal={setShowModal} />}

    </>
  )
}

export default IncidentReview;

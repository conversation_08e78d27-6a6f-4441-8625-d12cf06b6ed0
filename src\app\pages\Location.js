import React, { useState, useEffect } from "react";
import CreatableSelect from 'react-select/creatable';
import ListBox from "../form-elements/ListBox";
import { LOCATION1_URL, LOCATION1_WITH_ID_URL, LOCATION_CONFIG_URL, LOCATION_TIER1_URL, TIER1_TIER2_URL, TIER2_TIER3_URL, TEST_CASE_URL, TEST_CASE_WITH_ID_URL, TIER3_TIER4_URL, CHECKLIST_URL, DOCUMENTS_URL, EDIT_TIER_URL, SUBMIT_URL, DYNAMIC_TITLES_URL, TIER4_TIER5_URL, DYNAMIC_TITLES_WITH_ID } from "../constants";

import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import LocationList from "../form-elements/LocationList";
import { deletePopup, singlePopup } from "../notifications/Swal";
import { Alert } from "react-bootstrap";
import SubmitCard from "../dashboard/SubmitCard";
import AddItem from "../form-elements/AddItem";
import API from "../services/API";

const Location = () => {
    const [locations, setLocations] = useState([]);
    const [locationModal, setLocationModal] = useState(false);
    const [activityModal, setActivityModal] = useState(false);
    const [selectedLocation, setSelectedLocation] = useState({ value: '', label: '' });


    useEffect(() => {
        getLocations();

    }, [])

    const getLocations = async () => {


        const response = await API.get(LOCATION1_URL);
        if (response.status === 200) {
            const locationData = response.data;
            setLocations(locationData.map(i => { return { value: i.id, label: i.name, editable: true } }))
        }
    }

    const [submitted, setSubmitted] = useState([]);
    const [filterSubmit, setFilterSubmit] = useState([]);
    useEffect(() => {
        getSubmit();
    }, [])

    const getSubmit = async () => {
        const response = await API.get(SUBMIT_URL);
        if (response.status === 200) {
            const data = response.data;
            setSubmitted(data);


        }
    }

    const handleLocationCreate = async (newValue) => {
        const response = await API.post(LOCATION1_URL, { name: newValue });

        if (response.status === 200) {
            const newLocation = response.data;
            cogoToast.info('Location Created', { position: 'top-right' })
            setLocations((prev) => [{ value: newLocation.id, label: newValue, editable: true }, ...prev]);
        }

    }

    const handleLocationDelete = async (id) => {
        const response = await API.delete(LOCATION1_WITH_ID_URL(id))
        if (response.status === 204) {
            setLocations((prev) => prev.filter(i => i.value !== id));
            setSelectedLocation({ value: '', label: '' });
            singlePopup.fire(
                'Deleted!',
                '',
                'success'
            );
        }
    }

    const handleLocationEdit = async (value, id) => {
        const response = await API.patch(LOCATION1_WITH_ID_URL(id), { name: value })
        if (response.status === 204) {
            setLocations((prev) => prev.map(i => {

                if (i.value === id) {
                    i.label = value
                }
                return i;
            }));
            cogoToast.info('Location Updated', { position: 'top-right' })
        }
    }

    const handleLocationChange = (newValue, newAction) => {
        setSelectedLocation(newValue);

    }

    const [title, setTitles] = useState({ tier1: 'Tier I', tier2: 'Tier II', tier3: 'Tier III', tier4: 'Tier IV', tier5: 'Tier V' });
    const [titleData, setTitleData] = useState([])
    useEffect(() => {
        if (selectedLocation.value !== '')
            getLocationConfigs();
    }, [selectedLocation.value])

    const getLocationConfigs = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);

        if (response.status === 200) {
            const titles = response.data;
            setTitleData(titles)
            const locationsObject = titles.reduce((obj, item) => {
                obj[item.title] = item.altTitle;
                
                return obj;
            }, {});
            setTitles({ tier1: locationsObject.LocationTwo, tier2: locationsObject.LocationThree, tier3: locationsObject.LocationFour, tier4: locationsObject.LocationFive, tier5: locationsObject.LocationSix })
        }


    }


    const getLocationTier1 = async () => {
        const response = await API.get(LOCATION_TIER1_URL(selectedLocation.value));
        if (response.status === 200) {
            const tier1 = response.data;
            setTier1(tier1);
        }
    }

    const [tier1, setTier1] = useState([]);
    const [selectedTier1, setSelectedTier1] = useState({ id: '' });

    useEffect(() => {
        if (selectedLocation.value !== '')
            getLocationTier1();
        setSelectedTier1({ id: '' });
        setSelectedTier2({ id: '' });
        setSelectedTier3({ id: '' });
        setSelectedTier4({ id: '' });
        setSelectedTier5({ id: '' });
    }, [selectedLocation.value])

    const handleTier1Select = (id) => {
        setSelectedTier1(tier1.find(i => i.id === id))
    }

    const createTier1 = async (value) => {
        const response = await API.post(LOCATION_TIER1_URL(selectedLocation.value), { name: value })
        if (response.status === 200) {
            const createdTier1 = response.data;
            setTier1((prev) => [...prev, createdTier1]);
            cogoToast.info('Created!', { position: 'top-right' })

        }

    }

    const getTier1Tier2 = async () => {
        const response = await API.get(TIER1_TIER2_URL(selectedTier1.id));
        if (response.status === 200) {
            const tier2 = response.data;
            setTier2(tier2);
        }
    }

    const [tier2, setTier2] = useState([]);
    const [selectedTier2, setSelectedTier2] = useState({ id: '' });

    useEffect(() => {
        if (selectedTier1.id !== '')
            getTier1Tier2();
        setSelectedTier2({ id: '' });
        setSelectedTier3({ id: '' });
        setSelectedTier4({ id: '' });
        setSelectedTier5({ id: '' });

    }, [selectedTier1.id])

    const handleTier2Select = (id) => {
        setSelectedTier2(tier2.find(i => i.id === id))
    }

    const createTier2 = async (value) => {
        const response = await API.post(TIER1_TIER2_URL(selectedTier1.id), { name: value })
        if (response.status === 200) {
            const createdTier2 = response.data;
            setTier2((prev) => [...prev, createdTier2]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }

    const getTier2Tier3 = async () => {
        const response = await API.get(TIER2_TIER3_URL(selectedTier2.id));
        if (response.status === 200) {
            const tier3 = response.data;
            setTier3(tier3);
        }
    }

    const [tier3, setTier3] = useState([]);
    const [selectedTier3, setSelectedTier3] = useState({ id: '' });

    useEffect(() => {
        if (selectedTier2.id !== '')
            getTier2Tier3();
        setSelectedTier3({ id: '' });
        setSelectedTier4({ id: '' });
        setSelectedTier5({ id: '' });

    }, [selectedTier2.id])

    const handleTier3Select = (id) => {
        setSelectedTier3(tier3.find(i => i.id === id))
    }

    const createTier3 = async (value) => {
        const response = await API.post(TIER2_TIER3_URL(selectedTier2.id), { name: value })
        if (response.status === 200) {
            const createdTier3 = response.data;
            setTier3((prev) => [...prev, createdTier3]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }

    //Start
    const getTier3Tier4 = async () => {
        const response = await API.get(TIER3_TIER4_URL(selectedTier3.id));
        if (response.status === 200) {
            const tier4 = response.data;
            setTier4(tier4);
        }
    }

    const [tier4, setTier4] = useState([]);
    const [selectedTier4, setSelectedTier4] = useState({ id: '' });
    useEffect(() => {
        if (selectedTier3.id !== '')
            getTier3Tier4();
        setSelectedTier4({ id: '' });
        setSelectedTier5({ id: '' });

    }, [selectedTier3.id])

    const handleTier4Select = (id) => {
        setSelectedTier4(tier4.find(i => i.id === id))
    }

    const createTier4 = async (value) => {
        const response = await API.post(TIER3_TIER4_URL(selectedTier3.id), { name: value })
        if (response.status === 200) {
            const createdTier4 = response.data;
            setTier4((prev) => [...prev, createdTier4]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }
//end

const getTier4Tier5 = async () => {
    const response = await API.get(TIER4_TIER5_URL(selectedTier4.id));
    if (response.status === 200) {
        const tier5 = response.data;
        setTier5(tier5);
    }
}

const [tier5, setTier5] = useState([]);
const [selectedTier5, setSelectedTier5] = useState({ id: '' });
useEffect(() => {
    if (selectedTier4.id !== '')
        getTier4Tier5();
    
    setSelectedTier5({ id: '' });

}, [selectedTier4.id])

const handleTier5Select = (id) => {

    setSelectedTier5(tier5.find(i => i.id === id))
}

const createTier5 = async (value) => {
    const response = await API.post(TIER4_TIER5_URL(selectedTier4.id), { name: value })
    if (response.status === 200) {
        const createdTier5 = response.data;
        setTier5((prev) => [...prev, createdTier5]);
        cogoToast.info('Created!', { position: 'top-right' })
    }
}

//end

    const handleDeleteItem = async (mode, id) => {

        deletePopup.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                //   deleteChecklist(id);

                const response = await API.delete(EDIT_TIER_URL(mode, id))

                if (response.status === 204) {
                    switch (mode) {
                        case 'tier1':
                            setTier1(prev => prev.filter(i => i.id !== id))
                            setSelectedTier1({ id: '' });
                            setSelectedTier2({ id: '' });
                            setSelectedTier3({ id: '' });
                            setSelectedTier4({ id: '' });
                            setSelectedTier5({ id: '' });
                            break;
                        case 'tier2':
                            setTier2(prev => prev.filter(i => i.id !== id))
                            setSelectedTier2({ id: '' });
                            setSelectedTier3({ id: '' });
                            setSelectedTier4({ id: '' });
                            setSelectedTier5({ id: '' });

                            break;
                        case 'tier3':
                            setTier3(prev => prev.filter(i => i.id !== id))
                            setSelectedTier3({ id: '' });
                            setSelectedTier4({ id: '' });
                            setSelectedTier5({ id: '' });

                            break;
                        case 'tier4':

                            setTier4(prev => prev.filter(i => i.id !== id))
                            setSelectedTier4({ id: '' });
                            setSelectedTier5({ id: '' });
                            break;

                        case 'tier5':

                            setTier5(prev => prev.filter(i => i.id !== id))
                            setSelectedTier5({ id: '' });
                            break;

                        default: break;
                    }
                    singlePopup.fire(
                        'Deleted!',
                        '',
                        'success'
                    );
                }

            }
        })

    }

    const handleOnEditTitle = async (value, mode) => {
        let id = '';
        switch(mode) {
            case 'tier1': id = titleData.find(i => i.title === 'LocationTwo').id; break;
            case 'tier2': id = titleData.find(i => i.title === 'LocationThree').id; break;
            case 'tier3': id = titleData.find(i => i.title === 'LocationFour').id; break;
            case 'tier4': id = titleData.find(i => i.title === 'LocationFive').id; break;
            case 'tier5': id = titleData.find(i => i.title === 'LocationSix').id; break;
        }
        const response = await fetch(DYNAMIC_TITLES_WITH_ID(id), {
            method: 'PATCH',
            body: JSON.stringify({
                altTitle: value
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8"
            }
        })

        if (response.ok) {
            cogoToast.info('Updated', { position: 'top-right' })
        }
    }


    return (
        <>
            <div className="">
                <div className="col-lg-12 p-0">


                    <h4 className="card-title">Configure Organization Structure</h4>

                    <div className="my-3">
                        <div className="row">
                            <div className="col-md-3">
                                <label className="my-2"> Country <i onClick={() => setLocationModal(true)} className="typcn typcn-cog h3 text-primary cursor-pointer"></i></label>

                                <CreatableSelect
                                    isMulti={false}
                                    onCreateOption={handleLocationCreate}
                                    isSearchable={true}
                                    onChange={handleLocationChange}
                                    options={locations}
                                />


                            </div>
                        </div>

                    </div>

                    {selectedLocation.value && (
                        <>
                            <div className="row">
                                <div className="col p-1 ps-3">
                                    <ListBox handleDeleteItem={handleDeleteItem} location={selectedLocation.value} changeTitle={(id, value) => setTier1(prev => prev.map(i => { return i.id === id ? { ...i, title: value } : i }))} title={title.tier1} onHandleCreateItem={createTier1} lists={tier1} selected={selectedLocation.value !== ''} handleSelect={handleTier1Select} selectedItem={selectedTier1} onEditTitle={handleOnEditTitle} mode='tier1' />
                                </div>
                                <div className="col p-1">
                                    <ListBox handleDeleteItem={handleDeleteItem} location={selectedLocation.value} changeTitle={(id, value) => setTier2(prev => prev.map(i => { return i.id === id ? { ...i, title: value } : i }))} title={title.tier2} onHandleCreateItem={createTier2} lists={tier2} selected={selectedTier1.id !== ''} handleSelect={handleTier2Select} selectedItem={selectedTier2} onEditTitle={handleOnEditTitle} mode='tier2' />
                                </div>
                                <div className="col p-1">
                                    <ListBox handleDeleteItem={handleDeleteItem} location={selectedLocation.value} changeTitle={(id, value) => setTier3(prev => prev.map(i => { return i.id === id ? { ...i, title: value } : i }))} title={title.tier3} onHandleCreateItem={createTier3} lists={tier3} selected={selectedTier2.id !== ''} handleSelect={handleTier3Select} selectedItem={selectedTier3} onEditTitle={handleOnEditTitle} mode='tier3' />
                                </div>
                                <div className="col p-1">
                                    <ListBox handleDeleteItem={handleDeleteItem} location={selectedLocation.value} changeTitle={(id, value) => setTier4(prev => prev.map(i => { return i.id === id ? { ...i, title: value } : i }))} title={title.tier4} onHandleCreateItem={createTier4} lists={tier4} selected={selectedTier3.id !== ''} handleSelect={handleTier4Select} selectedItem={selectedTier4} onEditTitle={handleOnEditTitle} mode='tier4' />
                                </div>

                                <div className="col p-1 pe-3">
                                    <ListBox handleDeleteItem={handleDeleteItem} location={selectedLocation.value} changeTitle={(id, value) => setTier5(prev => prev.map(i => { return i.id === id ? { ...i, title: value } : i }))} title={title.tier5} onHandleCreateItem={createTier5} lists={tier5} selected={selectedTier4.id !== ''} handleSelect={handleTier5Select} selectedItem={selectedTier5} onEditTitle={handleOnEditTitle} mode='tier5' />
                                </div>


                            </div>
                        </>
                    )}

                    {!selectedLocation.value &&

                        (<div className="w-25">
                            <Alert variant="primary">
                                Please Select Location from above to Continue
                            </Alert>
                        </div>)}



                </div>
            </div>

            <Modal
                show={locationModal}
                onHide={() => setLocationModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    Country Configuration
                </Modal.Header>
                <Modal.Body>
                    <AddItem onCreate={handleLocationCreate} placeholder={'Enter Country Name'} />
                    <LocationList onLocationEdit={handleLocationEdit} handleDelete={handleLocationDelete} lists={locations} />
                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => setLocationModal(false)}>Close</Button>


                </Modal.Footer>
            </Modal>

            <Modal
                show={activityModal}
                onHide={() => setActivityModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    Work Order History
                </Modal.Header>
                <Modal.Body>
                    {
                        filterSubmit.map(i => {
                            return (
                                <SubmitCard key={i.id} data={i} />
                            )
                        })
                    }
                    {/* <SubmitCard /> */}
                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => setActivityModal(false)}>Close</Button>


                </Modal.Footer>
            </Modal>
        </>
    );

}

export default Location;
// @ts-nocheck
import React, { useState, useRef } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { INDIA_USERS_URL, INTERNAL_USERS_URL, KOREA_USERS_URL, UK_USERS_URL, USERS_URL, USERS_URL_WITH_ID } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@material-ui/core';

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-danger',
    cancelButton: 'btn btn-light'
  },
  buttonsStyling: false
})

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-primary',

  },
  buttonsStyling: false
})

const UkUser = () => {

  const [mdShow, setMdShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const history = useHistory();
  const uName = useRef();
  const uEmail = useRef();
  const uCompany = useRef();

  const uRole = useRef();

  const thead = [
    'Name',
    'Email',
    'Country Assigned',
    'Actions',

  ];


  $(document).on('click', '.unassign-user', async function (e) {


    deletePopup.fire({
      title: 'Are you sure?',
      text: "",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Unassign'
    }).then(async (result) => {
      if (result.isConfirmed) {
        //   deleteChecklist(id);
        const id = $(this).data('id')
        const response = await API.delete(USERS_URL_WITH_ID(id))

        if (response.status === 204) {

          $(this).removeClass('text-success').removeClass('mdi-check-circle').removeClass('unassign-user').addClass('assign-user').addClass('mdi-plus-circle').addClass('text-danger');
          cogoToast.info('User unassigned', { position: 'top-right' })

        }

      }
    })







  })


  $(document).on('click', '.assign-user', async function (e) {
    const response = await fetch(
      USERS_URL,
      {
        method: 'POST',
        body: JSON.stringify({
          id: $(this).data('id'),
          firstName: $(this).data('name'),
          email: $(this).data('email'),
          company: 'STT GDC',
          password: 'DEFAULTDISABLED',
          country: 'UK'


        }),
        headers: { "Content-type": "application/json; charset=UTF-8", "Authorization": "Bearer " + localStorage.getItem("access_token") }
      })

    if (response.ok) {
      $(this).addClass('text-success').addClass('mdi-check-circle').addClass('unassign-user').removeClass('assign-user').removeClass('mdi-plus-circle').removeClass('text-danger');
      cogoToast.info('User added to the Application!', { position: 'top-right' })
    } else {
      //show error
      cogoToast.error('Please try again', { position: 'top-right' })
    }




  })

 

  const options = {
    "initComplete": function (settings, json) {
      var searchInput = $('input[type="search"]');
      searchInput.attr('id', 'searchInput');
    },
    "processing": true,
    "serverSide": true,
    "ajax": {
      url: UK_USERS_URL,
      type: "GET",
      "headers": {
        "Authorization": "Bearer " + localStorage.getItem("access_token"),

      },

      "data": function (d) {
        d.search = $('input[type="search"]').val();
        d.limit = d.length;
        d.offset = d.start;
        return d;
      },

      dataSrc: 'data',
      "error": function (xhr, error, thrown) {
        try {
          console.log(xhr.responseText, 'here');

          if (xhr.status === 401) {
            history.push('/logout')
          }

          // alert("An error occurred while fetching data. Please try again later.");
        } catch (e) {
          console.log(e);
        }
      }
    },

    "columns": [{
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="username" data-id="${data.id}" class="edit-inline text-wrap width-200" contenteditable="true"> ${data.displayName ? data.displayName : ''} </div>`;

      }
    },
    {
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="email" data-id="${data.id}" class="edit-inline text-wrap width-200" contenteditable="true"> ${data.mail ? data.mail : ''} </div>`;

      }
    },

    {
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="country" data-id="${data.id}" class="text-wrap width-200"> ${data.country ? data.country : ''} </div>`;

      }
    },


    {
      "data": null,
      "render": function (data, type, full, meta) {
        if (data.status === 'active') {
          return `<div style="font-size: 22px;">
                                    <i class="mdi mdi-check-circle text-success unassign-user cursor-pointer" data-id="${data.id}" ></i></div>`;
        } else {
          return `<div style="font-size: 22px;">
                                    <i class="mdi mdi-plus-circle text-danger assign-user cursor-pointer" data-id="${data.id}" data-email="${data.mail}" data-name="${data.displayName}" ></i></div>`;
        }

      }
    },
    ]
  }


  return (
    <>
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body">

                <h4 className="card-title">STT Employee Database User Management</h4>
                <div className="row">
                  <div className="col-12">
                    <div>
                      {/* <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setMdShow(true); }}><i className="mdi mdi-account-plus mr-2" /> Create New User</button> */}

                      <DataTables thead={thead} options={options} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      

      <Modal
        show={mdShow}
        size="md"
        onHide={() => setMdShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >

        <Modal.Body>
          {/* <form className="forms">
            <div className="form-group">
              <label htmlFor="user_name" >Name</label>
              <Form.Control type="text" ref={uName} id="user_name" placeholder="Enter User Name" />
            </div>

            <div className="form-group">
              <label htmlFor="user_category" >Email</label>
              <Form.Control type="email" ref={uEmail} id="user_category" placeholder="Enter User Email" />
            </div>

            <div className="form-group">
              <label htmlFor="user_description" >Company</label>
              <Form.Control type="text" ref={uCompany} id="user_description" placeholder="Enter Company Name" />
            </div>

            <div className="form-group">
              <label htmlFor="user_description" >Assign Role</label>
              <select ref={uRole} className='form-control'>
                <option value=''>Choose Role</option>
                <option value='manager'>Manager</option>
                <option value='contractor'>Contractors</option>
              </select>
            </div>



          </form> */}
        </Modal.Body>

        <Modal.Footer className="flex-wrap">

          <>
            <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>

          </>


        </Modal.Footer>
      </Modal>
    </>
  )
}


export default UkUser;

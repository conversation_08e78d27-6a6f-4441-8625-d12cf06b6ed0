import React from "react";

export const IncidentActionCard = ({ actions }) => {
    return (<>
        <div className="obs-section p-4">




            <>


                {actions.map((action, i) => {

                    if (action.actionType === 'take_actions_control' ||  action.actionType === 'take_actions_ra') {

                        k = k + 1;

                        return (
                            <div className="obs-section p-4">
                                <div className="row mb-3">
                                    <div className="col-md-12">
                                        {k === 1 ?
                                            <>
                                                <p className="obs-title"> Assigned Action {reportData.maskId} - A{k} </p>
                                                <p className="obs-content">{action.actionToBeTaken}</p>
                                                {action.status === 'open' && <>
                                                    <p className="obs-title"> Action Assignee</p>
                                                    <p className="obs-content">{action.assignedToId &&
                                                        getName(action.assignedToId
                                                        )}</p>
                                                </>}
                                            </>
                                            :
                                            <>
                                                <p className="obs-title"> Action Reviewer Comments & Reassigned Action {reportData.maskId} - A{k} </p>
                                                <p className="obs-content">{action.comments}</p>
                                                {action.status === 'open' && <>
                                                    <p className="obs-title"> Action Assignee</p>
                                                    <p className="obs-content">{action.assignedToId &&
                                                        getName(action.assignedToId
                                                        )}</p>
                                                </>}
                                            </>
                                        }
                                    </div>
                                </div>


                                {action.status === 'submitted' && <>
                                    <div className="row mb-3">
                                        <div className="col-md-12">
                                            <p className="obs-title">Action Taken </p>
                                            <p className="obs-content">{action.actionTaken}</p>
                                        </div>
                                    </div>

                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Action Taken By</p>
                                            <p className="obs-content">{action.assignedToId &&
                                                getName(action.assignedToId
                                                )}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p className="obs-title">Date</p>
                                            <p className="obs-content">{getClosedDate(action)}</p>
                                        </div>
                                    </div>

                                    <div className="col-md-12">

                                        {action.uploads && <>
                                            <p className="obs-title">Evidence</p>
                                            {action.uploads.map(item => (<>
                                                <GalleryPage photos={[{
                                                    src: `${STATIC_URL}/${item}`,
                                                    width: 4,
                                                    height: 3
                                                }]} />

                                            </>

                                            ))}</>
                                        }



                                    </div>
                                </>
                                }

                            </div>
                        )


                    } else if (action.actionType === 'retake_actions' && action.status === 'submitted') {

                        return (
                            <div className="obs-section p-4">

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Action Reviewed By</p>
                                        <p className="obs-content">{getName(action.assignedToId)}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Date</p>
                                        <p className="obs-content">{moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a')}</p>
                                    </div>
                                </div>


                            </div>
                        )
                    } else if (action.actionType === 'verify_actions' && action.status === 'submitted') {
                        return (
                            <div className="obs-section p-4">


                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Action Reviewed By</p>
                                        <p className="obs-content">{reportData.reviewerId && getName(reportData.reviewerId)}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Date</p>
                                        <p className="obs-content">{moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a')}</p>
                                    </div>
                                </div>
                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Action Reviewer Comments</p>
                                        <p className="obs-content">{action.comments}</p>
                                    </div>
                                </div>







                            </div>
                        )
                    }

                })}




            </>





        </div>
    </>)
}

export default IncidentActionCard;
import React, { useEffect, useState, useMemo } from 'react'
import API from '../services/API';
import { Modal, Button, Form } from 'react-bootstrap';
import { Button as B1 } from 'primereact/button'
import { useHistory } from "react-router-dom";
import { ALL_INCIDENT_URL, AUDIT_URL, INSPECTION_URL, MY_AUDIT_URL, AUDIT_WITH_ID_URL, AUDIT_LIST } from '../constants';
import AuditForm from './AuditForm';
import { useSelector } from "react-redux";
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import cogoToast from 'cogo-toast';
import IncidentTriggerModal from './IncidentTriggerModal';
import AllFilterLocation from './AllLocationFilter';
import IncidentInformationModal from './IncidentInformationModal';
import ActionTable from './ActionTable';
import moment from 'moment';
import AuditPanel from './AuditPanel';
import AuditFindings from './AuditFindings';
import AuditFindingPanel from './AuditFindingPanel';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import Swal from 'sweetalert2';

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});
const AuditList = () => {

    const me = useSelector((state) => state.login.user)
    const isAuditorScheduler = useMemo(() => {
        return me?.validationRoles?.some(item => item.name === 'Audit Scheduler') || false;
    }, [me]);

    const [popupData, setPopupData] = useState([]);
    const [assigned, setAssigned] = useState([])
    const [actionModal, setActionModal] = useState(false)
    const [actionData, setActionData] = useState([])
    const [addModal, setAddModal] = useState(false)
    const [location, setLocation] = useState(false)

    const [status, setStatus] = useState([])
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();
    const years = [currentYear, currentYear + 1];
    const [selectedYear, setSelectedYear] = useState(currentYear);

    const handleActionCard = (data) => {
        setActionModal(true)
        setActionData(data)
    }

    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'maskId': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.title': { value: null, matchMode: FilterMatchMode.IN },
        'assignedTo.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'checklist.name': { value: null, matchMode: FilterMatchMode.IN },
        'locationFour.name': { value: null, matchMode: FilterMatchMode.IN },
        description: { value: null, matchMode: FilterMatchMode.IN },
        dueDate: { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
    });

    const [auditModal, setAuditModal] = useState(false)

    const [selectedAudit, setSelectedAudit] = useState('')

    const handleAudit = (id) => {
        setSelectedAudit(id)
        setAuditModal(true)
    }

    const [auditFindingModal, setAuditFindingModal] = useState('')


    const handleAuditFindings = (id) => {
        setSelectedAudit(id)
        setAuditFindingModal(true)
    }





    const [data, setData] = useState([]);
    const [filterData, setFilterData] = useState([]);


    useEffect(() => {
        getAuditData();
    }, [])

    const getAuditData = async () => {
        const params = {
            "include": [{ "relation": "assignedTo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "checklist" }]

        };
        const response = await API.get(`${AUDIT_LIST}?filter=${encodeURIComponent(JSON.stringify(params))}`);


        if (response.status === 200) {
            const transformedData = response.data
                .filter(item => item.status !== 'Submitted' && item.status !== 'Cancelled')
                .map(item => {
                    if (item.status === 'Initiated') {
                        return { ...item, status: 'Scheduled' };
                    } else if (item.status === 'Draft') {
                        return { ...item, status: 'Under Progress' };
                    }
                    return item;
                })
                .reverse();

            setData(transformedData);
            setFilterData(transformedData);

            const obs = response.data.map(item => {
                const firstName = item.assignedTo ? item.assignedTo.firstName || '' : '';
                return { name: firstName, value: firstName };
            });
            setAssigned(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));

            const obs1 = response.data.map(item => {
                const firstName = item.locationFour.name ? item.locationFour.name || '' : '';
                return { name: firstName, value: firstName };
            });
            setLocation(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));

            const obs2 = response.data.map(item => {
                const firstName = item.status ? item.status === 'Initiated' ? 'Scheduled' : item.status === 'Draft' ? 'Under Progress' : 'Re-Scheduled' || '' : '';
                return { name: firstName, value: firstName };
            });
            setStatus(obs2.filter((ele, ind) => ind === obs2.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));
        }
    }


    const handleSubmit = async (formData) => {
        formData.year = `${selectedYear}`;
        formData.status = 'Initiated'
        console.log(formData)

        try {
            const response = await API.post(AUDIT_URL, formData);
            if (response.status === 200) {
                cogoToast.success('Submitted!');
                // getChecklist();
                getAuditData();

                setAddModal(false);
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            customSwal2.fire("The selected auditor is pre-assigned to another audit on one or more of these dates. Select another date or assign a different auditor for these dates", "", "warning");
            // cogoToast.error("The selected auditor is pre-assigned to another audit on one or more of these dates. Select another date or assign a different auditor for these dates");
        }

    }

    const handleReSubmit = async (formData) => {
        formData.year = `${selectedYear}`;
        formData.status = 'Re-Scheduled'
        console.log(formData)
        try {

            const response = await API.patch(AUDIT_WITH_ID_URL(popupData.id), formData)
            if (response.status === 204) {
                cogoToast.success('ReSubmitted!')
                getAuditData();
                setAddModal(false)
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            customSwal2.fire("The selected auditor is pre-assigned to another audit on one or more of these dates. Select another date or assign a different auditor for these dates", "", "warning");
        }
    }

    const handleDeleteSubmit = async (formData) => {

        const data = { status: 'Cancelled' }


        const response = await API.patch(AUDIT_WITH_ID_URL(popupData.id), data)
        if (response.status === 204) {
            cogoToast.success('Deleted !')
            getAuditData();
            setAddModal(false)
        }
    }

    const openAuditDetails = (data) => {
        setAddModal(true)
        setPopupData(data)

    }

    // const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
    //     const filteredData = data.filter(item => {
    //         return (
    //             (locationOneId === '' || item.locationOneId === locationOneId) &&
    //             (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
    //             (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
    //             (locationFourId === '' || item.locationFourId === locationFourId)
    //         );
    //     });

    //     setFilterData(filteredData);
    // };

    const viewTemplate = (option) => {
        return (
            <>
                {option.status === 'Initiated' && <i style={{ fontSize: '22px' }} onClick={(e) => handleAudit(option.id)} className='cursor-pointer mdi mdi-text-box-check-outline'></i>}

                <i style={{ fontSize: '22px' }} onClick={(e) => handleAuditFindings(option.id)} className='cursor-pointer ms-2 mdi mdi-account-plus'></i>
            </>
        )

    }
    const maskIdBodyTemplate = (row) => {

    
            return (
                <div className='maskid' onClick={(e) => openAuditDetails(row)} >
                    {row.maskId}
                </div>
            );
        



    }

    const dateFromFormat = (row) => {
        return moment(row.dateTime, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
    }

    const dateToFormat = (row) => {
        return moment(row.endDateTime, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY');
    }
    const filterOptions = [
        { label: "<EMAIL>", value: "<EMAIL>" },
        { label: "Mani Govindan", value: "Mani Govindan" },
        { label: "Jc (Acuizen)", value: "Jc (Acuizen)" },
        { label: "Samson Joseph", value: "Samson Joseph" },
        { label: "Varun (Acuizen)", value: "Varun (Acuizen)" },
        { label: "Phyllis Teo", value: "Phyllis Teo" },
        { label: "", value: "" }
    ];
    const assignedFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <MultiSelect value={options.value} options={assigned} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const statusFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <MultiSelect value={options.value} options={status} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const locationFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <MultiSelect value={options.value} options={location} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const checklistFilterTemplate = (options) => {

        return (

            <React.Fragment>
                <MultiSelect value={options.value} options={[{ name: 'GMS Assurance Audit', value: 'GMS Assurance Audit' }, { name: 'EHS Management System Audit', value: 'EHS Management System Audit' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>

        );
    }

    const renderHeader = () => {


        return (
            <div className='d-flex justify-content-end align-items-end'>
                {isAuditorScheduler && <B1 label="Add New" icon="pi pi-plus" className=" btn btn-primary" onClick={() => { setAddModal(true); setPopupData([]) }} />}

            </div>
        );
    };
    const statusBodyTemplate = (row) => {
        if (row.status === 'Initiated') {
            return 'Scheduled'
        }
        return row.status


    }
    const header = renderHeader();
    return (
        <>


            <div>



                <DataTable
                    value={filterData}
                    paginator
                    rows={10}
                    filters={filters}
                    header={header}
                    // onFilter={(e) => {
                    //     console.log(e);
                    //     setFilters(e.filters);
                    // }}
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                    rowsPerPageOptions={[10, 25, 50]}
                    emptyMessage="No Data found."
                    globalFilterFields={['maskId', 'checklist.name', 'locationFour.name', 'dateTime', 'endDateTime', 'assignedTo.firstName', 'status']}
                >

                    <Column field='maskId' body={maskIdBodyTemplate} header="Audit ID" sortable></Column>
                    <Column field='checklist.name' filter filterElement={checklistFilterTemplate} header='Audit Type' showFilterMatchModes={false}></Column>
                    <Column field='locationFour.name' filter filterElement={locationFilterTemplate} header="Project / DC / Office" showFilterMatchModes={false}></Column>
                    <Column field='dateTime' body={dateFromFormat} header="From" ></Column>
                    <Column field='endDateTime' body={dateToFormat} header="To" ></Column>
                    <Column field='assignedTo.firstName' header="Assigned Auditor" filter filterElement={assignedFilterTemplate} showFilterMatchModes={false}></Column>
                    <Column field='status' header="Status" body={statusBodyTemplate} filter filterElement={statusFilterTemplate} showFilterMatchModes={false}></Column>


                </DataTable>



            </div>


            {/* <IncidentTriggerModal triggerShowModal={triggerShowModal} setTriggerShowModal={setTriggerShowModal} incidentId={triggerInvestigationId} /> */}
            <Modal
                show={actionModal}
                size="lg"
                onHide={() => setActionModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>

                    <ActionTable totalActions={actionData} />
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setActionModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>

            {selectedAudit && <Modal
                show={auditModal}
                size="lg"
                onHide={() => setActionModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <AuditPanel auditModal={auditModal} setAuditModal={setAuditModal} auditId={selectedAudit} />

                </Modal.Body>

            </Modal>}

            {selectedAudit && <Modal
                show={auditFindingModal}
                size="lg"
                onHide={() => setAuditFindingModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <AuditFindingPanel auditId={selectedAudit} />

                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setAuditFindingModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>}

            {(addModal) && <AuditForm
                show={addModal}
                onHide={() => setAddModal(false)}
                formData={popupData}
                // month={popupData.month}
                // year={selectedYear}
                // checklist={popupData.checklist}
                // location={popupData.location}
                onSubmit={(formData) => {

                    handleSubmit(formData)
                }}

                reSubmit={(formData) => {

                    handleReSubmit(formData)
                }}
                deleteSubmit={(formData) => {

                    handleDeleteSubmit(formData)
                }}
            />}


        </>
    )
}

export default AuditList;

import React, { useState, useEffect } from 'react';
// import moment from 'moment';
import { USERS_URL, STATIC_URL, ALL_USERS_BY_LOCATION, EPTW_CONTROL_MEASURE_ID } from '../constants';
import API from '../services/API';
import { Accordion, AccordionTab } from 'primereact/accordion';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import GalleryPage from '../apps/Gallery';
import Typography from '@mui/material/Typography'
import moment from 'moment-timezone';
import { useSelector } from 'react-redux';
import { Button, Modal } from 'react-bootstrap';
import Select from 'react-select';
import Swal from 'sweetalert2';

const customFontStyle = {
  fontFamily: 'Lato, sans-serif',

};
const ActionTable = ({ totalActions, id, actions, current }) => {

  console.log(current);
  console.log('control')
  const user = useSelector((state) => state.login.user);
  const [users, setUsers] = useState([]);
  const [totalAction, setTotalAction] = useState([])
  const [showModal, setShowModal] = useState(false);
  const [showModalR, setShowModalR] = useState(false);
  const [readOnly, setReadOnly] = useState(false);
  const data = useSelector((state) => state.login.user);
  const [locationUsers, setLocationUsers] = useState([])
  const validationRoles = data?.validationRoles || [];
  const [controlMeasures, setControlMeasures] = useState([]);
  const [riskAssessments, setRiskAssessments] = useState([]);
  const [validationErrors, setValidationErrors] = useState([]);
  const [validationControlErrors, setValidationControlErrors] = useState([]);
  let cm = 0.0;
  let doc = 0.0;
  let picm = 0.0;
  let nc = 0.0

  let rass = 0.0;
  let cmss = 0.0;
  let poss = 0.0
  let ncs = 0.0

  useEffect(() => {
    getAllUsers();

    setTotalAction(actions); // Log totalActions for debugging or potential use
  }, []);

  useEffect(() => {
    const fetchUsers = async () => {
      try {

        const result = await API.post(ALL_USERS_BY_LOCATION, { locationOneId: current.locationOneId, locationTwoId: current.locationTwoId, locationThreeId: current.locationThreeId, locationFourId: current.locationFourId });
        setLocationUsers(result.data.map(user => ({
          value: user.id,
          label: user.firstName
        })));
      } catch (error) {
        console.error('Error fetching data', error);
      }
    };
    if (current.locationOneId && current.locationTwoId && current.locationThreeId && current.locationFourId)
      fetchUsers();

  }, [current])
  const convertToLocalTime = (gmtDate) => {
    // Get the system's time zone
    const systemTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Define formats to handle
    const customFormat = 'DD-MM-YYYY HH:mm'; // Format for "23-07-2024 13:35"

    let localDate;

    if (moment(gmtDate, customFormat, true).isValid()) {
      // If the input matches the custom format
      localDate = moment.tz(gmtDate, customFormat, 'GMT').tz(systemTimeZone).format('Do MMM YYYY, hh:mm A');
    } else if (moment(gmtDate).isValid()) {
      // If the input is a valid ISO 8601 date
      localDate = moment.tz(gmtDate, 'GMT').tz(systemTimeZone).format('Do MMM YYYY, hh:mm A');
    } else {
      throw new Error('Invalid date format');
    }

    return localDate;
  };
  const handleFieldChange = (index, field, value) => {
    const updatedMeasures = [...controlMeasures];
    updatedMeasures[index][field] = value;
    setControlMeasures(updatedMeasures);
  };

  const handleAddControlMeasure = () => {
    setControlMeasures([
      ...controlMeasures,
      { controlMeasures: "", completionDate: "", personResponsible: "" },
    ]);
    setValidationControlErrors([
      ...validationControlErrors,
      { controlMeasures: false, completionDate: false, personResponsible: false },
    ]);
  };

  // Handle Delete Control Measure
  const handleDeleteControlMeasure = (index) => {
    const updatedMeasures = controlMeasures.filter((_, i) => i !== index);
    const updatedErrors = validationControlErrors.filter((_, i) => i !== index);
    setControlMeasures(updatedMeasures);
    setValidationControlErrors(updatedErrors);
  };

  const validateFieldsControl = () => {
    const errors = controlMeasures.map((item) => ({
      controlMeasures: !item.controlMeasures.trim(),
      completionDate: !item.completionDate.trim(),
      personResponsible: !item.personResponsible,
    }));
    setValidationControlErrors(errors);
    return errors.every((error) => !Object.values(error).includes(true));
  };

  // Example usage
  // const gmtDate = "2024-11-06T05:27:29.857Z";
  // console.log(convertToLocalTime(gmtDate)); // Outputs local time in the desired format



  const getAllUsers = async () => {
    const response = await API.get(USERS_URL);
    setUsers(response.data);
  };

  const getName = (id) => {
    const user = users.find(user => user.id === id);
    return user?.firstName || '';
  };

  const getActioNumber = (action, index) => {

    if (index === 'cm') {
      cmss = cmss + 1.0
      let status = ''
      action.data.map(item => {
        if (item.actionType === 'take_actions_control') {
          status = 'CM ' + cmss.toFixed(1)
        } else if (item.actionType === 'retake_actions') {
          status = 'CM ' + (cmss + 0.1)
        }
      })
      return status
    }
    if (index === 'ra') {
      rass = rass + 1.0
      let status = ''
      action.data.map(item => {
        if (item.actionType === 'take_actions_ra') {
          status = 'DOC ' + rass.toFixed(1)
        } else if (item.actionType === 'retake_actions') {
          status = 'DOC ' + (rass + 0.1)
        }
      })
      return status
    }
    if (index === 'picm') {
      poss = poss + 1.0
      let status = ''
      action.data.map(item => {
        if (item.actionType === 'take_actions_control_post') {
          status = 'PICM ' + poss.toFixed(1)
        } else if (item.actionType === 'retake_actions') {
          status = 'PICM ' + (poss + 0.1)
        }
      })
      return status
    }
    if (index === 'nc') {
      ncs = ncs + 1.0
      let status = ''
      action.data.map(item => {
        if (item.actionType === 'audit_take_actions') {
          status = 'NC ' + ncs.toFixed(1)
        } else if (item.actionType === 'retake_actions') {
          status = 'NC ' + (ncs + 0.1)
        }
      })
      return status
    }

  }

  const getActionDescription = (action, index) => {
    if (index === 'cm') {
      // cmss = cmss + 1.0
      let status = ''
      action.data.map(item => {
        if (item.actionType === 'take_actions_control') {
          status = item.actionToBeTaken
        } else if (item.actionType === 'retake_actions') {
          status = item.actionToBeTaken
        }
      })
      return status
    }
    if (index === 'ra') {
      // rass = rass + 1.0
      let status = ''
      action.data.map(item => {
        if (item.actionType === 'take_actions_ra') {
          status = item.actionToBeTaken
        } else if (item.actionType === 'retake_actions') {
          status = item.actionToBeTaken
        }
      })
      return status
    }
    if (index === 'picm') {
      // poss = poss + 1.0
      let status = ''
      action.data.map(item => {
        if (item.actionType === 'take_actions_control_post') {
          status = item.actionToBeTaken
        } else if (item.actionType === 'retake_actions') {
          status = item.actionToBeTaken
        }
      })
      return status
    }
    if (index === 'nc') {
      // ncs = ncs + 1.0
      let status = ''
      action.data.map(item => {
        if (item.actionType === 'audit_take_actions') {
          status = item.actionToBeTaken
        } else if (item.actionType === 'retake_actions') {
          status = item.actionToBeTaken
        }
      })
      return status
    }
  }
  const getStatusAction = (action, index) => {

    let status = '';
    let ras = false;
    let cms = false;
    let pos = false;
    let ncs = false


    if (action.firstActionType === 'take_actions_control') {

      switch (action.lastActionType) {
        case 'take_actions_control':
          status = 'Immediate Control Measures Assigned'

          break;
        case 'retake_actions':
          status = 'Immediate Control Measures Reassigned'

          break
        case 'verify_actions':
          status = 'Immediate Control Measures Implemented - Pending Verification'
          break

        case 'approve':
          status = 'Immediate Control Measure: Verified & Closed'
          break
        default:

          break;
      }

      cms = true
    }
    else if (action.firstActionType === 'take_actions_ra') {
      switch (action.lastActionType) {
        case 'take_actions_ra':
          status = 'RA / SWP changes assigned'
          break;
        case 'retake_actions':
          status = 'RA / SWP changes Re-assigned'
          break
        case 'verify_actions':
          status = 'RA / SWP chages done - Pending Verification'
          break

        case 'approve':
          status = 'RA / SWP changes: Verified & Closed'
          break
      }
      ras = true;
    }
    else if (action.firstActionType === 'take_actions_control_post') {
      switch (action.lastActionType) {
        case 'take_actions_control_post':
          status = 'Post Investigation Control Measures Assigned'
          break;
        case 'retake_actions':
          status = 'Post Investigation Control Measures Re-Assigned'
          break
        case 'verify_actions':
          status = 'Post Investigation Control Measures - Pending Verification'
          break
        case 'approve':
          status = 'Post Investigation Control Measure: Verified & Closed'
          break
      }
      pos = true
    }
    else if (action.firstActionType === 'audit_take_actions') {
      switch (action.lastActionType) {
        case 'audit_take_actions':
          status = 'Audit Action Assigned'
          break;
        case 'aud_retake_actions':
          status = 'Audit Action Re-Assigned'
          break
        case 'aud_verify_actions':
          status = 'Audit Action - Pending Verification'
          break
        case 'approve':
          status = 'Audit Action: Verified & Closed'
          break
      }
      cms = true
    }

    return (

      <Typography variant="body1" style={customFontStyle}>
        <div className='row d-flex mb-2'>

          <div className='col-6'>

            <h4>{getActioNumber(action, index)}</h4>
            <p className='p-0' style={{ fontSize: '14px' }}>{getActionDescription(action, index)}</p>


          </div>
          <div className='col-6'>
            <span className={`badge fw-bold ${ras ? 'status-tag-orange' : cms ? 'status-tag-blue' : pos ? 'status-tag-pink' : ''}`}>
              {status}
            </span>
          </div>
        </div>
        <div className='row d-flex' >

          <div className='col-6'>


            {showUserByStatus(action)}


          </div>
          <div className='col-6'>
            <h6>DueDate : {moment(action.data[0].dueDate, ['DD/MM/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD']).format('Do MMM YYYY')}</h6>
          </div>
        </div>

      </Typography>
    )


  }
  const showUserByStatus = (action) => {


    switch (action.lastActionType) {
      case "take_actions_control":
      case "take_actions_ra":
      case "audit_take_actions":
      case "take_actions_control_post":
        return `Action Assignee : ${getName(action.lastAssignedToId)}`;
      case "verify_actions":
        return `Action to be verified by : ${getName(action.lastAssignedToId)}`;
      case "aud_verify_actions":
        return `Action to be verified by : ${getName(action.lastAssignedToId)}`;
      case "retake_actions":
        return `Action Re-assigned to : ${getName(action.lastAssignedToId)}`;
      case "aud_retake_actions":
        return `Action Re-assigned to : ${getName(action.lastAssignedToId)}`;
      case "approve":
        return `Action Verified By : ${getName(action.lastAssignedToId)}`;
      default:
        return ''; // Handle default case if needed
    }
  };


  const getCMData = (data) => {

    return data.data.map((item, index) => {
      if (item.actionType === "take_actions_control") {

        let [main, float] = cm.toString().split('.').map(Number);
        console.log(main, float)
        main = main + 1.0

        cm = main
        console.log(cm)
        return (
          <div className="obs-section p-4">
            <div key={index} className="row ">
              <div className='col-8'>
                <p className="obs-title"> Assigned Action - CM {cm.toFixed(1)} </p>
                <p className="obs-content">{item.actionToBeTaken}</p>
              </div>

              {item.status === 'open' && <>
                <p className="obs-title"> Action Assignee</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </>}
              {item.status === 'submitted' && <>
                <div className="row mb-3">
                  <div className="col-md-12">
                    <p className="obs-title">Action Taken </p>
                    <p className="obs-content">{item.actionTaken}</p>
                  </div>
                </div>

                <div className="row mb-3">
                  <div className="col-md-6">
                    <p className="obs-title">Action Taken By</p>
                    <p className="obs-content">{item.assignedToId &&
                      getName(item.assignedToId
                      )}</p>
                  </div>
                  <div className="col-md-6">
                    <p className="obs-title">Date</p>
                    <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
                  </div>
                </div>

              </>
              }
            </div>
            <div className='row'>


              <div className="col-md-12">

                {item.uploads && item.uploads.length > 0 && (<>
                  <div className='d-flex'>
                    <p className="obs-title"> Evidence</p>
                    {item.uploads.map((upload) => {
                      const fileExtension = upload.split('.').pop().toLowerCase();

                      if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                        // Handle image files using GalleryPage
                        return (

                          <GalleryPage
                            photos={[{
                              src: `${STATIC_URL}/${upload}`,
                              width: 4,
                              height: 3
                            }]}
                            key={upload} // Use upload as key for uniqueness
                          />
                        );
                      } else if (fileExtension === 'pdf') {
                        // Handle PDF files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              View PDF
                            </a>
                          </div>
                        );
                      } else if (['xls', 'xlsx'].includes(fileExtension)) {
                        // Handle Excel files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              Download Excel File
                            </a>
                          </div>
                        );
                      } else {
                        // Handle other file types
                        return (
                          <div className="col-md-3" key={upload}>
                            <p>Unsupported file type: {fileExtension}</p>
                          </div>
                        );
                      }
                    })}
                  </div>

                </>)}
              </div>
            </div>
          </div>

        );

      } else if (item.actionType === "retake_actions") {

        cm = cm + 0.1

        return (

          <div className="obs-section p-4">
            <div className='row'>
              <div className='col-6'>
                <p className="obs-title"> Action Verifier Comments & Reassigned Action  - CM {cm} </p>
                <p className="obs-content">{item.comments}</p>

                <p className="obs-title"> Action Taken </p>
                <p className="obs-content">{item.actionTaken}</p>
                {item.status === 'open' ?
                  <p className="obs-title"> Action Assignee</p>
                  : <p className="obs-title"> Action Taken By</p>}
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </div>
              <div className='col-6'>
                <p className="obs-title">Date</p>
                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
              </div>
            </div>
            <div className='row'>
              <div className="col-md-12">
                {item.uploads && item.uploads.length > 0 && (<>
                  <div className='d-flex'>
                    <p className="obs-title"> Images</p>
                    {item.uploads.map((upload) => {
                      const fileExtension = upload.split('.').pop().toLowerCase();

                      if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                        // Handle image files using GalleryPage
                        return (

                          <GalleryPage
                            photos={[{
                              src: `${STATIC_URL}/${upload}`,
                              width: 4,
                              height: 3
                            }]}
                            key={upload} // Use upload as key for uniqueness
                          />
                        );
                      } else if (fileExtension === 'pdf') {
                        // Handle PDF files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              View PDF
                            </a>
                          </div>
                        );
                      } else if (['xls', 'xlsx'].includes(fileExtension)) {
                        // Handle Excel files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              Download Excel File
                            </a>
                          </div>
                        );
                      } else {
                        // Handle other file types
                        return (
                          <div className="col-md-3" key={upload}>
                            <p>Unsupported file type: {fileExtension}</p>
                          </div>
                        );
                      }
                    })}
                  </div>

                </>)}
              </div>
            </div>
          </div>
        );
      } else if (item.actionType === "approve" && item.status === 'submitted') {


        return (
          <div className="obs-section p-4">


            <div className="row mb-3">
              <div className="col-md-6">
                <p className="obs-title">Action Verified By</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </div>
              <div className="col-md-6">
                <p className="obs-title">Date</p>
                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
              </div>
            </div>
            <div className="row mb-3">
              <div className="col-md-6">
                <p className="obs-title">Action Verifier Comments</p>
                <p className="obs-content">{item.comments}</p>
              </div>
            </div>



          </div>
        );
      } else if (item.actionType === 'reject' && item.status === 'submitted') {

        return (
          <div className="obs-section p-4">
            <div className='row'>

              <div className="col-md-6">
                <p className="obs-title">Action Verified By</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </div>
              <div className="col-md-6">
                <p className="obs-title">Date</p>
                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
              </div>
            </div>
          </div>
        )
      }
      return null; // Handle other cases if necessary
    });


  };

  const getRAData = (data) => {
    // doc = doc + 1.0
    return data.data.map((item, index) => {
      if (item.actionType === "take_actions_ra") {

        let [main, float] = doc.toString().split('.').map(Number);
        console.log(main, float)
        main = main + 1.0

        doc = main


        return (
          <div className="obs-section p-4">
            <div key={index} className="row ">
              <div className='col-8'>
                <p className="obs-title"> Assigned Action - DOC {doc.toFixed(1)} </p>
                <p className="obs-content">{item.actionToBeTaken}</p>
              </div>


              {item.status === 'open' && <>
                <p className="obs-title"> Action Assignee</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </>}
              {item.status === 'submitted' && <>
                <div className="row mb-3">
                  <div className="col-md-12">
                    <p className="obs-title">Action Taken </p>
                    <p className="obs-content">{item.actionTaken}</p>
                  </div>
                </div>

                <div className="row mb-3">
                  <div className="col-md-6">
                    <p className="obs-title">Action Taken By</p>
                    <p className="obs-content">{item.assignedToId &&
                      getName(item.assignedToId
                      )}</p>
                  </div>
                  <div className="col-md-6">
                    <p className="obs-title">Date</p>
                    <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
                  </div>
                </div>

              </>
              }
            </div>
            <div className='row'>


              <div className="col-md-12">

                {item.uploads && item.uploads.length > 0 && (<>
                  <div className='d-flex'>
                    <p className="obs-title"> Evidences</p>
                    {item.uploads.map((upload) => {
                      const fileExtension = upload.split('.').pop().toLowerCase();

                      if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                        // Handle image files using GalleryPage
                        return (

                          <GalleryPage
                            photos={[{
                              src: `${STATIC_URL}/${upload}`,
                              width: 4,
                              height: 3
                            }]}
                            key={upload} // Use upload as key for uniqueness
                          />
                        );
                      } else if (fileExtension === 'pdf') {
                        // Handle PDF files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              View PDF
                            </a>
                          </div>
                        );
                      } else if (['xls', 'xlsx'].includes(fileExtension)) {
                        // Handle Excel files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              Download Excel File
                            </a>
                          </div>
                        );
                      } else {
                        // Handle other file types
                        return (
                          <div className="col-md-3" key={upload}>
                            <p>Unsupported file type: {fileExtension}</p>
                          </div>
                        );
                      }
                    })}
                  </div>

                </>)}
              </div>
            </div>
          </div>

        );

      } else if (item.actionType === "retake_actions") {

        doc = doc + 0.1

        return (

          <div className="obs-section p-4">

            <div className='row'>
              <p className="obs-title"> Action Verifier Comments & Reassigned Action  - DOC {doc} </p>
              <p className="obs-content">{item.comments}</p>
              <p className="obs-title"> Action Taken </p>
              <p className="obs-content">{item.actionTaken}</p>
              {item.status === 'open' ?
                <p className="obs-title"> Action Assignee</p>
                : <p className="obs-title"> Action Taken By</p>}
              <p className="obs-content">{item.assignedToId &&
                getName(item.assignedToId
                )}</p>

            </div>
            <div className='row'>


              <div className="col-md-12">

                {item.uploads && item.uploads.length > 0 && (<>
                  <div className='d-flex'>
                    <p className="obs-title"> Images</p>
                    {item.uploads.map((upload) => {
                      const fileExtension = upload.split('.').pop().toLowerCase();

                      if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                        // Handle image files using GalleryPage
                        return (

                          <GalleryPage
                            photos={[{
                              src: `${STATIC_URL}/${upload}`,
                              width: 4,
                              height: 3
                            }]}
                            key={upload} // Use upload as key for uniqueness
                          />
                        );
                      } else if (fileExtension === 'pdf') {
                        // Handle PDF files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              View PDF
                            </a>
                          </div>
                        );
                      } else if (['xls', 'xlsx'].includes(fileExtension)) {
                        // Handle Excel files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              Download Excel File
                            </a>
                          </div>
                        );
                      } else {
                        // Handle other file types
                        return (
                          <div className="col-md-3" key={upload}>
                            <p>Unsupported file type: {fileExtension}</p>
                          </div>
                        );
                      }
                    })}
                  </div>

                </>)}
              </div>
            </div>

          </div>
        );
      } else if (item.actionType === "approve" && item.status === 'submitted') {



        return (
          <div className="obs-section p-4">


            <div className="row mb-3">
              <div className="col-md-6">
                <p className="obs-title">Action Verified By</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </div>
              <div className="col-md-6">
                <p className="obs-title">Date</p>
                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
              </div>
            </div>
            <div className="row mb-3">
              <div className="col-md-6">
                <p className="obs-title">Action Verifier Comments</p>
                <p className="obs-content">{item.comments}</p>
              </div>
            </div>



          </div>
        );
      } else if (item.actionType === 'reject' && item.status === 'submitted') {

        return (
          <div className="obs-section p-4">


            <div className="col-md-6">
              <p className="obs-title">Action Verified By</p>
              <p className="obs-content">{item.assignedToId &&
                getName(item.assignedToId
                )}</p>
            </div>
          </div>
        )
      }
      return null; // Handle other cases if necessary
    });
  };
  const getPicmData = (data) => {
    // picm = picm + 1.0
    return data.data.map((item, index) => {
      if (item.actionType === "take_actions_control_post") {
        let [main, float] = picm.toString().split('.').map(Number);
        console.log(main, float)
        main = main + 1.0

        picm = main
        return (
          <div className="obs-section p-4">
            <div key={index} className="row ">
              <div className='col-8'>
                <p className="obs-title"> Assigned Action - PICM {picm.toFixed(1)} </p>
                <p className="obs-content">{item.actionToBeTaken}</p>
              </div>


              {item.status === 'open' && <>
                <p className="obs-title"> Action Assignee</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </>}
              {item.status === 'submitted' && <>
                <div className="row mb-3">
                  <div className="col-md-12">
                    <p className="obs-title">Action Taken </p>
                    <p className="obs-content">{item.actionTaken}</p>
                  </div>
                </div>

                <div className="row mb-3">
                  <div className="col-md-6">
                    <p className="obs-title">Action Taken By</p>
                    <p className="obs-content">{item.assignedToId &&
                      getName(item.assignedToId
                      )}</p>
                  </div>
                  <div className="col-md-6">
                    <p className="obs-title">Date</p>
                    <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
                  </div>
                </div>

              </>
              }
            </div>
            <div className='row'>


              <div className="col-md-12">

                {item.uploads && item.uploads.length > 0 && (<>
                  <div className='d-flex'>
                    <p className="obs-title"> Images</p>
                    {item.uploads.map((upload) => {
                      const fileExtension = upload.split('.').pop().toLowerCase();

                      if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                        // Handle image files using GalleryPage
                        return (

                          <GalleryPage
                            photos={[{
                              src: `${STATIC_URL}/${upload}`,
                              width: 4,
                              height: 3
                            }]}
                            key={upload} // Use upload as key for uniqueness
                          />
                        );
                      } else if (fileExtension === 'pdf') {
                        // Handle PDF files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              View PDF
                            </a>
                          </div>
                        );
                      } else if (['xls', 'xlsx'].includes(fileExtension)) {
                        // Handle Excel files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              Download Excel File
                            </a>
                          </div>
                        );
                      } else {
                        // Handle other file types
                        return (
                          <div className="col-md-3" key={upload}>
                            <p>Unsupported file type: {fileExtension}</p>
                          </div>
                        );
                      }
                    })}
                  </div>

                </>)}
              </div>
            </div>
          </div>

        );

      } else if (item.actionType === "retake_actions") {

        picm = picm + 0.1

        return (

          <div className="obs-section p-4">

            <div className='row'>
              <p className="obs-title"> Action Verifier Comments & Reassigned Action  - PICM {picm} </p>
              <p className="obs-content">{item.comments}</p>

              <p className="obs-title"> Action Taken </p>
              <p className="obs-content">{item.actionTaken}</p>
              {item.status === 'open' ?
                <p className="obs-title"> Action Assignee</p>
                : <p className="obs-title"> Action Taken By</p>}
              <p className="obs-content">{item.assignedToId &&
                getName(item.assignedToId
                )}</p>

            </div>
            <div className='row'>


              <div className="col-md-12">

                {item.uploads && item.uploads.length > 0 && (<>
                  <div className='d-flex'>
                    <p className="obs-title"> Images</p>
                    {item.uploads.map((upload) => {
                      const fileExtension = upload.split('.').pop().toLowerCase();

                      if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                        // Handle image files using GalleryPage
                        return (

                          <GalleryPage
                            photos={[{
                              src: `${STATIC_URL}/${upload}`,
                              width: 4,
                              height: 3
                            }]}
                            key={upload} // Use upload as key for uniqueness
                          />
                        );
                      } else if (fileExtension === 'pdf') {
                        // Handle PDF files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              View PDF
                            </a>
                          </div>
                        );
                      } else if (['xls', 'xlsx'].includes(fileExtension)) {
                        // Handle Excel files
                        return (
                          <div className="col-md-3" key={upload}>
                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                              Download Excel File
                            </a>
                          </div>
                        );
                      } else {
                        // Handle other file types
                        return (
                          <div className="col-md-3" key={upload}>
                            <p>Unsupported file type: {fileExtension}</p>
                          </div>
                        );
                      }
                    })}
                  </div>

                </>)}
              </div>
            </div>

          </div>
        );
      } else if (item.actionType === "approve" && item.status === 'submitted') {



        return (
          <div className="obs-section p-4">


            <div className="row mb-3">
              <div className="col-md-6">
                <p className="obs-title">Action Verified By</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </div>
              <div className="col-md-6">
                <p className="obs-title">Date</p>
                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
              </div>
            </div>
            <div className="row mb-3">
              <div className="col-md-6">
                <p className="obs-title">Action Verifier Comments</p>
                <p className="obs-content">{item.comments}</p>
              </div>
            </div>



          </div>
        );
      } else if (item.actionType === 'reject' && item.status === 'submitted') {

        return (
          <div className="obs-section p-4">


            <div className="col-md-6">
              <p className="obs-title">Action Verified By</p>
              <p className="obs-content">{item.assignedToId &&
                getName(item.assignedToId
                )}</p>
            </div>
          </div>
        )
      }
      return null; // Handle other cases if necessary
    });
  };
  const isJSON = (str) => {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  };
  const getNcData = (data) => {
    nc = nc + 1.0
    return data.data.map((item, index) => {
      if (item.actionType === "audit_take_actions") {

        return (
          <div className="obs-section p-4">
            <div key={index} className="row ">
              <div className="row mb-4 p-2" style={{ border: '1px solid #e7e6e6' }}>
                <div className="col-6">
                  <p>Project/DC: <b>{current.locationFour.name}</b></p>
                  <p>Start Date: <b>{moment(current.dateTime, "DD/MM/YYYY").format("Do MMM YYYY")}</b></p>

                </div>
                <div className="col-6">
                  <p>Auditor Name: <b>{current.assignedTo?.firstName}</b></p>
                  <p>End Date: <b>{moment(current.endDateTime, "DD/MM/YYYY").format("Do MMM YYYY")}</b></p>
                </div>
                <div className="col-12">
                  <p>Findings: <b>{item.applicationDetails?.findings}</b></p>
                </div>

                <div className="col-6">
                  <p>Category: <b>{item.applicationDetails?.category}</b></p>
                  <p>Standards And References: <b>{item.applicationDetails?.standardsAndReferences}</b></p>
                  <p>Recommended Mitigation Measures: <b>{item.applicationDetails?.recommendations}</b></p>
                </div>
                <div className="col-6">
                  <p>Classification: <b>{item.applicationDetails?.classification}</b></p>
                  <p>Potential Consequences: <b>{item.applicationDetails?.potentialHazard}</b></p>
                  <p>Due Date: <b>{item.dueDate}</b></p>
                </div>

              </div>
              <div className='col-8'>
                <p className="obs-title"> Assigned Action - NC {nc.toFixed(1)} </p>
                <p className="obs-content">{item.actionToBeTaken}</p>
              </div>


              {item.status === 'open' && <>
                <p className="obs-title"> Action Assignee</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </>}
              {item.status === 'submitted' && <>
                <div className="row mb-3">
                  {isJSON(item.actionTaken) ? <>

                    <div className="col-md-6">

                      <p className='obs-title'>Identify the Root Cause(s)</p>

                      <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).rootCause }} />
                    </div>
                    <div className="col-md-6">

                      <p className='obs-title'>Identified Corrective Actions</p>
                      <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).correctiveAction }} />
                      {/* <p className="obs-content">{JSON.parse(item.actionTaken).correctiveAction}</p> */}
                    </div>
                    <div className="col-md-6">

                      <p className='obs-title'>Description of the Action Taken</p>
                      <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).actionDesc }} />
                      {/* <p className="obs-content">{JSON.parse(item.actionTaken).actionDesc}</p> */}
                    </div>
                  </>
                    :
                    <div className="col-md-12">
                      <p className="obs-title">Action Taken </p>
                      <p className="obs-content">{item.actionTaken}</p>


                    </div>
                  }

                </div>

                <div className="row mb-3">
                  <div className="col-md-6">
                    <p className="obs-title">Action Taken By</p>
                    <p className="obs-content">{item.assignedToId &&
                      getName(item.assignedToId
                      )}</p>
                  </div>
                  <div className="col-md-6">
                    <p className="obs-title">Date</p>
                    <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
                  </div>
                </div>

              </>
              }
            </div>
            <div className='row'>


              <div className="col-md-12">

                {item.uploads && item.uploads.length > 0 && (
                  <>
                    <div className='d-flex'>
                      <p className="obs-title">Files</p>
                      {item.uploads.map(file => {
                        const fileExtension = file.split('.').pop().toLowerCase();

                        if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                          // Handle image files
                          return (
                            <GalleryPage
                              photos={[{
                                src: `${STATIC_URL}/${file}`,
                                width: 4,
                                height: 3
                              }]}
                            />
                          );
                        } else if (fileExtension === 'pdf') {
                          // Handle PDF files (as URLs)
                          return (
                            <a href={`${STATIC_URL}/${file}`} target="_blank" rel="noopener noreferrer">
                              View PDF
                            </a>
                          );
                        } else if (['xls', 'xlsx'].includes(fileExtension)) {
                          // Handle Excel files
                          return (
                            <a href={`${STATIC_URL}/${file}`} target="_blank" rel="noopener noreferrer">
                              Download Excel File
                            </a>
                          );
                        } else {
                          // Handle other file types or show a default message
                          return (
                            <p>Unsupported file type: {fileExtension}</p>
                          );
                        }
                      })}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

        );

      } else if (item.actionType === "aud_retake_actions") {

        nc = nc + 0.1

        return (

          <div className="obs-section p-4">

            <div className='row'>
              <p className="obs-title"> Action Verifier Comments & Reassigned Action  - NC {nc} </p>
              <p className="obs-content">{item.comments}</p>


              {item.status === 'open' ?
                <p className="obs-title"> Action Assignee</p>
                : <p className="obs-title"> Action Taken By</p>}
              <p className="obs-content">{item.assignedToId &&
                getName(item.assignedToId
                )}</p>

            </div>

            <div className="row mb-3">
              {isJSON(item.actionTaken) && <>

                <div className="col-md-6">

                  <p className='obs-title'>Identify the Root Cause(s)</p>

                  <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).rootCause }}></p>
                </div>
                <div className="col-md-6">

                  <p className='obs-title'>Identified Corrective Actions</p>

                  <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).correctiveAction }}></p>
                </div>
                <div className="col-md-6">

                  <p className='obs-title'>Description of the Action Taken</p>

                  <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).actionDesc }}></p>
                </div>
              </>

              }

            </div>
            <div className='row'>


              <div className="col-md-12">

                {item.uploads && item.uploads.length > 0 && (
                  <>
                    <div className='d-flex'>
                      <p className="obs-title">Files</p>
                      {item.uploads.map(file => {
                        const fileExtension = file.split('.').pop().toLowerCase();

                        if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                          // Handle image files
                          return (
                            <GalleryPage
                              photos={[{
                                src: `${STATIC_URL}/${file}`,
                                width: 4,
                                height: 3
                              }]}
                            />
                          );
                        } else if (fileExtension === 'pdf') {
                          // Handle PDF files (as URLs)
                          return (
                            <a href={`${STATIC_URL}/${file}`} target="_blank" rel="noopener noreferrer">
                              View PDF
                            </a>
                          );
                        } else if (['xls', 'xlsx'].includes(fileExtension)) {
                          // Handle Excel files
                          return (
                            <a href={`${STATIC_URL}/${file}`} target="_blank" rel="noopener noreferrer">
                              Download Excel File
                            </a>
                          );
                        } else {
                          // Handle other file types or show a default message
                          return (
                            <p>Unsupported file type: {fileExtension}</p>
                          );
                        }
                      })}
                    </div>
                  </>
                )}
              </div>
            </div>

          </div>
        );
      } else if (item.actionType === "approve" && item.status === 'submitted') {



        return (
          <div className="obs-section p-4">


            <div className="row mb-3">
              <div className="col-md-6">
                <p className="obs-title">Action Verified By</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </div>
              <div className="col-md-6">
                <p className="obs-title">Date</p>
                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
              </div>
            </div>
            <div className="row mb-3">
              <div className="col-md-6">
                <p className="obs-title">Action Verifier Comments</p>
                <p className="obs-content">{item.comments}</p>
              </div>
            </div>



          </div>
        );
      } else if (item.actionType === 'reject' && item.status === 'submitted') {

        return (
          <div className="obs-section p-4">

            <div className='row'>
              <div className="col-md-6">
                <p className="obs-title">Action Verified By</p>
                <p className="obs-content">{item.assignedToId &&
                  getName(item.assignedToId
                  )}</p>
              </div>
              <div className="col-md-6">
                <p className="obs-title">Date</p>
                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
              </div>
            </div>
          </div>
        )
      }
      return null; // Handle other cases if necessary
    });
  };
  const toggleModal = () => {
    setShowModal(!showModal);
    setValidationControlErrors([]); // Reset validation errors when closing modal
  };

  const toggleModalR = () => {
    setShowModalR(!showModalR);
    setValidationErrors([]); // Reset validation errors when closing modal
  };

  const handleSaveChanges = async (data, type) => {
    try {
      // Prepare the payload based on the type
      const payload = type === "control" ? { riskControl: { controlMeasures: data } } : { riskControl: { riskAssessment: data } };

      // Make the API call
      const response = await API.patch(EPTW_CONTROL_MEASURE_ID(current.id), payload);

      if (response.status === 204) {
        console.log("Data updated successfully");

        // Show SweetAlert confirmation
        Swal.fire({
          title: "Success!",
          text: "Data updated successfully",
          icon: "success",
          confirmButtonText: "Ok",
        }).then((result) => {
          if (result.isConfirmed) {
             window.location.reload(); // Reload the page
          }
        });
      } else {
        console.error("Unexpected response status:", response.status);
      }
    } catch (error) {
      console.error("Error updating data:", error);
      Swal.fire({
        title: "Error!",
        text: "Something went wrong. Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };
  const handleAddRiskAssessment = () => {
    setRiskAssessments([
      ...riskAssessments,
      { name: "", completionDate: "", personResponsible: "" },
    ]);
    setValidationErrors([...validationErrors, { name: false, completionDate: false, personResponsible: false }]);
  };
  const handleDeleteRiskAssessment = (index) => {
    const updatedAssessments = riskAssessments.filter((_, i) => i !== index);
    const updatedErrors = validationErrors.filter((_, i) => i !== index);
    setRiskAssessments(updatedAssessments);
    setValidationErrors(updatedErrors);
  };

  const validateFields = () => {
    const errors = riskAssessments.map((item) => ({
      name: !item.name.trim(),
      completionDate: !item.completionDate.trim(),
      personResponsible: !item.personResponsible,
    }));
    setValidationErrors(errors);
    return errors.every((error) => !Object.values(error).includes(true));
  };

  const handleFieldChangeR = (index, field, value) => {
    const updatedAssessments = [...riskAssessments];
    updatedAssessments[index][field] = value;
    setRiskAssessments(updatedAssessments);
  };

  const handleSave = (type) => {
    if (type === 'control') {
      if (validateFieldsControl()) {
        handleSaveChanges(controlMeasures, type);

      }

    } else {
      if (validateFields()) {
        handleSaveChanges(riskAssessments, type);

      }
    }

  };
  return (<>
    <h4 className='mb-3 fw-bold'>{id} Actions</h4>
    {current?.maskId.startsWith("INC") && <>
      {(validationRoles.some(role => role.name === 'Country EHS Director') || user.id === current.incidentOwnerId || user.id === current.userId || user.id === current.reviewerId) && (
        <div className="d-flex justify-content-end mb-3">
          <Button onClick={() => setShowModal(true)} className="me-3">Add Control Measure</Button>
          <Button onClick={() => setShowModalR(true)}>Add RA / SWP</Button>
        </div>
      )}

    </>}

    <Accordion>

      {totalAction.map((action, index) => (



        action.firstActionType === 'take_actions_control' ? (
          <AccordionTab header={getStatusAction(action, 'cm')}>
            <>{getCMData(action)}</>
          </AccordionTab>
        ) : action.firstActionType === 'take_actions_ra' ? (
          <AccordionTab header={getStatusAction(action, 'ra')}>
            <>{getRAData(action)}</>
          </AccordionTab>
        ) : action.firstActionType === 'take_actions_control_post' ? (
          <AccordionTab header={getStatusAction(action, 'picm')}>
            <>{getPicmData(action)}</>
          </AccordionTab>
        ) : action.firstActionType === 'audit_take_actions' ? (
          <AccordionTab header={getStatusAction(action, 'nc')}>
            <>{getNcData(action)}</>
          </AccordionTab>
        ) : null



      ))
      }
    </Accordion >

    <Modal show={showModal} onHide={toggleModal} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>Control Measures</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {controlMeasures.length > 0 ? (
          controlMeasures.map((action, index) => (
            <div className="form-group d-flex align-items-center mb-3" key={index}>
              {/* Corrective/Control Measures */}
              <label className="me-2 w-50">
                Corrective/Control Measures:
                <input
                  className={`form-control ${validationControlErrors[index]?.controlMeasures ? "is-invalid" : ""
                    }`}
                  type="text"
                  value={action.controlMeasures}
                  onChange={(e) =>
                    handleFieldChange(index, "controlMeasures", e.target.value)
                  }
                  disabled={readOnly}
                />
                {validationControlErrors[index]?.controlMeasures && (
                  <div className="invalid-feedback">
                    This field is required.
                  </div>
                )}
              </label>

              {/* Due Date */}
              <label className="me-2">
                Due Date:
                {readOnly ? (
                  <input
                    className="form-control"
                    type="text"
                    value={
                      action.completionDate
                        ? moment(action.completionDate, "YYYY-MM-DD").format("Do MMM YYYY")
                        : ""
                    }
                    disabled
                  />
                ) : (
                  <input
                    className={`form-control ${validationControlErrors[index]?.completionDate ? "is-invalid" : ""
                      }`}
                    type="date"
                    value={action.completionDate}
                    onChange={(e) =>
                      handleFieldChange(index, "completionDate", e.target.value)
                    }
                    min={moment(current.incidentDate, "DD/MM/YYYY hh:mm A").format(
                      "YYYY-MM-DD"
                    )}
                  />
                )}
                {validationControlErrors[index]?.completionDate && (
                  <div className="invalid-feedback">Due Date is required.</div>
                )}
              </label>

              {/* Person Responsible */}
              <label className="w-25 me-2">
                Person Responsible:
                <Select
                  value={locationUsers.find(
                    (option) => option.value === action.personResponsible
                  )}
                  isDisabled={readOnly}
                  options={locationUsers.sort((a, b) =>
                    a.label.localeCompare(b.label)
                  )}
                  onChange={(selectedOption) =>
                    handleFieldChange(index, "personResponsible", selectedOption?.value)
                  }
                  placeholder="Choose"
                  className={
                    validationControlErrors[index]?.personResponsible ? "is-invalid" : ""
                  }
                />
                {validationControlErrors[index]?.personResponsible && (
                  <div className="invalid-feedback d-block">
                    Person Responsible is required.
                  </div>
                )}
              </label>

              {/* Delete Button */}
              {!readOnly && (
                <Button
                  variant="danger"
                  onClick={() => handleDeleteControlMeasure(index)}
                  className="btn-sm"
                >
                  <i className="mdi mdi-delete" />
                </Button>
              )}
            </div>
          ))
        ) : (
          <div className="text-center">
            {/* <p className="text-muted">No control measures added yet.</p> */}
            {!readOnly && (
              <Button variant="success" onClick={handleAddControlMeasure}>
                Add Control Measure
              </Button>
            )}
          </div>
        )}
        {!readOnly && controlMeasures.length > 0 && (
          <Button variant="success" onClick={handleAddControlMeasure}>
            Add Another Control Measure
          </Button>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={toggleModal}>
          Close
        </Button>
        {!readOnly && (
          <Button variant="primary" onClick={() => handleSave('control')}>
            Save Changes
          </Button>
        )}
      </Modal.Footer>
    </Modal>

    <Modal show={showModalR} onHide={toggleModalR} size="lg" >

      <Modal.Header closeButton>
        <Modal.Title>Risk Assessments</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {riskAssessments.length > 0 ? (
          riskAssessments.map((action, index) => (
            <div className="form-group d-flex align-items-center mb-3" key={index}>
              {/* Name of Risk Assessment */}
              <label className="me-2 w-50">
                Name of Risk Assessment / Safe Working Procedure:
                <input
                  className={`form-control ${validationErrors[index]?.name ? "is-invalid" : ""
                    }`}
                  type="text"
                  value={action.name}
                  onChange={(e) =>
                    handleFieldChangeR(index, "name", e.target.value)
                  }
                  disabled={readOnly}
                />
                {validationErrors[index]?.name && (
                  <div className="invalid-feedback">
                    This field is required.
                  </div>
                )}
              </label>

              {/* Due Date Field */}
              <label className="me-2">
                Due Date:
                {readOnly ? (
                  <input
                    className="form-control"
                    type="text"
                    value={
                      action.completionDate
                        ? moment(action.completionDate, "YYYY-MM-DD").format("Do MMM YYYY")
                        : ""
                    }
                    disabled
                  />
                ) : (
                  <input
                    className={`form-control ${validationErrors[index]?.completionDate ? "is-invalid" : ""
                      }`}
                    type="date"
                    value={action.completionDate}
                    onChange={(e) =>
                      handleFieldChangeR(index, "completionDate", e.target.value)
                    }
                    min={moment(current.incidentDate, "DD/MM/YYYY hh:mm A").format(
                      "YYYY-MM-DD"
                    )}
                  />
                )}
                {validationErrors[index]?.completionDate && (
                  <div className="invalid-feedback">
                    Due Date is required.
                  </div>
                )}
              </label>

              {/* Person Responsible Field */}
              <label className="w-25 me-2">
                Person Responsible:
                <Select
                  value={locationUsers.find(
                    (option) => option.value === action.personResponsible
                  )}
                  isDisabled={readOnly}
                  options={locationUsers.sort((a, b) =>
                    a.label.localeCompare(b.label)
                  )}
                  onChange={(selectedOption) =>
                    handleFieldChangeR(index, "personResponsible", selectedOption?.value)
                  }
                  placeholder="Choose"
                  className={
                    validationErrors[index]?.personResponsible ? "is-invalid" : ""
                  }
                />
                {validationErrors[index]?.personResponsible && (
                  <div className="invalid-feedback d-block">
                    Person Responsible is required.
                  </div>
                )}
              </label>

              {/* Delete Button */}
              {!readOnly && (
                <Button
                  variant="danger"
                  onClick={() => handleDeleteRiskAssessment(index)}
                  className="btn-sm"
                >
                  <i className="mdi mdi-delete" />
                </Button>
              )}
            </div>
          ))
        ) : (
          <div className="text-center">
            {/* <p className="text-muted">No risk assessments added yet.</p> */}
            {!readOnly && (
              <Button variant="success" onClick={handleAddRiskAssessment}>
                Add Risk Assessment
              </Button>
            )}
          </div>
        )}
        {!readOnly && riskAssessments.length > 0 && (
          <Button variant="success" onClick={handleAddRiskAssessment}>
            Add Another Risk Assessment
          </Button>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={toggleModalR}>
          Close
        </Button>
        {!readOnly && (
          <Button variant="primary" onClick={() => handleSave('ra')}>
            Save Changes
          </Button>
        )}
      </Modal.Footer>
    </Modal>

  </>
  );
};

export default ActionTable;

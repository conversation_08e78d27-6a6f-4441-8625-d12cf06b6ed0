import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>xis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const DynamicBarChart = ({ data, title, total, legend }) => {

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', { month: 'short', year: '2-digit' });
    };

    return (

        <>
            <div className=''>
                <h4 className='mb-3 text-center'>{title}* : {total}</h4>

            </div>

            <ResponsiveContainer width="100%" aspect={4.0 / 2.0}>
                <BarChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={formatDate} />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar name={legend} dataKey="value" fill="green" />
                </BarChart>
            </ResponsiveContainer>
        </>
    );
};

export default DynamicBar<PERSON>hart;
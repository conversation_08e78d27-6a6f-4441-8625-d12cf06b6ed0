import React, { Component } from "react";
import { Map, GoogleApi<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "google-maps-react";

class SatelliteMap extends Component {
    render() {
        const mapStyles = {
            width: "100%",
            height: "300px",
            display: "block",
            left: "-15px"
        };

        // Coordinates for Singapore
        const singaporeCoords = {
            lat: 1.3521,
            lng: 103.8198,
        };

        // Function to generate random coordinates within a given range
        const getRandomCoordinates = (center, range) => {
            const offset = range / 111300; // about 111300 meters in one degree
            const lat = center.lat + offset * (Math.random() * 2 - 1);
            const lng = center.lng + offset * (Math.random() * 2 - 1);
            return { lat, lng };
        };

        // Generate 10 random markers around Singapore
        const randomMarkers = Array.from({ length: 10 }).map((_, index) => (
            <Marker
                key={index}
                position={getRandomCoordinates(singaporeCoords, 5000)} // 5000 meters around Singapore
            />
        ));

        return (
            <div className="row">
                <div className="card" style={{height: '300px'}}>
                    <Map
                        
                        google={this.props.google}
                        zoom={12}
                        style={mapStyles}
                        initialCenter={singaporeCoords}
                        mapTypeId={"satellite"}
                    >
                        {randomMarkers}
                    </Map>
                </div>
            </div>

        );
    }
}

export default GoogleApiWrapper({
    apiKey: "AIzaSyC_XC6V1JS6-Y4aycuHppIxL-JMWjQGTnw",
})(SatelliteMap);

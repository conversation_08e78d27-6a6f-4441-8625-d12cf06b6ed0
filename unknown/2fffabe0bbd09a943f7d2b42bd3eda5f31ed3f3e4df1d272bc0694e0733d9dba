import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>A<PERSON><PERSON>, YA<PERSON><PERSON>, Tooltip, ResponsiveContainer, Legend, LabelList } from 'recharts';

const BarFive = () => {
    const data = [
        { month: 'Jan', unsafeAction: 40, unsafeCondition: 30 },
        { month: 'Feb', unsafeAction: 45, unsafeCondition: 35 },
        { month: 'Mar', unsafeAction: 50, unsafeCondition: 40 },
        { month: 'Apr', unsafeAction: 55, unsafeCondition: 45 },
        { month: 'May', unsafeAction: 60, unsafeCondition: 50 },
        { month: 'Jun', unsafeAction: 65, unsafeCondition: 55 },
        { month: 'Jul', unsafeAction: 70, unsafeCondition: 60 },
        { month: 'Aug', unsafeAction: 75, unsafeCondition: 65 },
        { month: 'Sep', unsafeAction: 80, unsafeCondition: 70 },
        { month: 'Oct', unsafeAction: 85, unsafeCondition: 75 },
        { month: 'Nov', unsafeAction: 90, unsafeCondition: 80 },
        { month: 'Dec', unsafeAction: 95, unsafeCondition: 85 }
    ];

    return (
        <ResponsiveContainer width="100%" height={500}>
            <BarChart data={data}>
                <XAxis dataKey="month" />
                <YAxis tickFormatter={() => ''} />
                <Tooltip />
                <Bar stackId="a" dataKey="unsafeAction" fill="#EE5724" radius={[0, 0, 0, 0]} barSize={30}>
                    <LabelList dataKey="unsafeAction" position="top" />
                </Bar>
                <Bar stackId="a" dataKey="unsafeCondition" fill="#FF9D7C" radius={[10, 10, 0, 0]} barSize={30}>
                    <LabelList dataKey="unsafeCondition" position="top" />
                </Bar>
                <Legend verticalAlign="bottom" align="center" />
            </BarChart>
        </ResponsiveContainer>
    );
};

export default BarFive;

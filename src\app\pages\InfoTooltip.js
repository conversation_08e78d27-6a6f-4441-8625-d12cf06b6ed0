import React from 'react';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';
import InfoIcon from '@mui/icons-material/Info';

const InfoTooltip = ({ content }) => {
  return (
    <Tooltip
      title={
        <div style={{ maxWidth: 1200, zIndex: 99999 }}>
          {content}
        </div>
      }
      arrow
    >
      <IconButton size="small">
        <InfoIcon fontSize="small" />
      </IconButton>
    </Tooltip>
  );
};

export default InfoTooltip;

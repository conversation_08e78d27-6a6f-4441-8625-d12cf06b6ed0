import React, { useEffect, useState, useMemo } from 'react'
import API from '../services/API';
import { Modal, Button, Form } from 'react-bootstrap';
import { useHistory } from "react-router-dom";
import { ALL_INCIDENT_URL, AUDIT_URL, INSPECTION_URL } from '../constants';
import moment from 'moment'
import AuditFindingPanel from './AuditFindingPanel';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import AuditPanel from './AuditPanel';
import { MultiSelect } from 'primereact/multiselect';
import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import ActionTable from './ActionTable';
const AuditTable = () => {

    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'maskId': { value: null, matchMode: FilterMatchMode.IN },
        'checklist.name': { value: null, matchMode: FilterMatchMode.IN },
        'locationFour.name': { value: null, matchMode: FilterMatchMode.IN },
        'assignedTo.firstName': { value: null, matchMode: FilterMatchMode.IN },
        description: { value: null, matchMode: FilterMatchMode.IN },
        dueDate: { value: null, matchMode: FilterMatchMode.IN },
        state: { value: null, matchMode: FilterMatchMode.IN },
    });

    const [auditActionModal, setAuditActionModal] = useState(false)
    const [auditFindingModal, setAuditFindingModal] = useState('')
    const [actionModal, setActionModal] = useState(false)
    const [actionData, setActionData] = useState([])
    const [assigned, setAssigned] = useState([])
    const [current, setCurrent] = useState([])
    const handleActionCard = (data) => {
        setActionModal(true)
        setActionData(data)
    }
    const [triggerInvestigationId, setTriggerInvestigationId] = useState(false)

    const handleTriggerInvestigation = (id) => {
        setTriggerInvestigationId(id)
        setTriggerShowModal(true)
    }

    const [data, setData] = useState([]);
    const [filterData, setFilterData] = useState([]);
    const [triggerShowModal, setTriggerShowModal] = useState(false)
    const [location, setLocation] = useState(false)
    useEffect(() => {
        getAuditData();
    }, [])

    const getAuditData = async () => {
        const params = {
            "include": [{ "relation": "assignedTo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "checklist" }]

        };
        const response = await API.get(`${AUDIT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);


        if (response.status === 200) {
            const submittedData = response.data.filter(item => item.status === 'Submitted').reverse();
            setData(submittedData);
            setFilterData(submittedData);
            const obs = response.data.map(item => {
                const firstName = item.assignedTo ? item.assignedTo.firstName || '' : '';
                return { name: firstName, value: firstName };
            });
            setAssigned(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));

            const obs1 = response.data.map(item => {
                const firstName = item.locationFour.name ? item.locationFour.name || '' : '';
                return { name: firstName, value: firstName };
            });
            setLocation(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));


        }
    }

    useEffect(() => {


    }, [filterData])


    const [showModal, setShowModal] = useState(false)

    const handleAuditFindings = (id) => {
        setSelectedAudit(id)
        setAuditFindingModal(true)
    }
    const [auditModal, setAuditModal] = useState(false)
    const [currentIncident, setCurrentIncident] = useState('')
    const [selectedAudit, setSelectedAudit] = useState(null)
    const viewAudit = (id) => {
        setSelectedAudit(id)
        setShowModal(true)
    }

    const handleAudit = (id) => {
        setSelectedAudit(id)
        setAuditModal(true)
    }
    const viewTemplate = (option) => {
        return (<>
            <i style={{ fontSize: '22px' }} onClick={(e) => handleAuditFindings(option.id)} className='cursor-pointer ms-2 mdi mdi-account-plus'></i>

            {/* <i style={{ fontSize: '22px' }} onClick={(e) => handleAudit(option.id)} className='cursor-pointer mdi mdi-text-box-check-outline'></i> */}
        </>
        )

    }
    const maskIdBodyTemplate = (row) => {

        return (
            <div className='maskid' onClick={() => viewAudit(row.id)}>
                {row.maskId}
            </div>
        );

    }
    const projectBodyTemplate = (row) => {

        return (
            <div >
                {row.locationOne.name} {'>'} {row.locationTwo.name} {'>'} {row.locationThree.name} {'>'} {row.locationFour.name}
            </div>
        );

    }

    const dateFromFormat = (row) => {
        return moment(row.dateTime, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
    }

    const dateToFormat = (row) => {
        return moment(row.endDateTime, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY');
    }
    const filterOptions = [
        { label: "<EMAIL>", value: "<EMAIL>" },
        { label: "Mani Govindan", value: "Mani Govindan" },
        { label: "Jc (Acuizen)", value: "Jc (Acuizen)" },
        { label: "Samson Joseph", value: "Samson Joseph" },
        { label: "Varun (Acuizen)", value: "Varun (Acuizen)" },
        { label: "Phyllis Teo", value: "Phyllis Teo" },
        { label: "", value: "" }
    ];
    const assignedFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <MultiSelect value={options.value} options={assigned} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const [totalAction, setTotalAction] = useState([])
    const [maskId, setMaskId] = useState(null)
    const customFilter = (value, filters) => {
        if (!filters || filters.length === 0) return true;
        return filters.some(filter => value?.assignedTo?.firstName?.includes(filter));
    };
    function groupByDescription(data) {
        const groupedData = [];
        const descriptionMap = {};

        data.forEach(item => {
            const { objectId, description, actionType, assignedToId, status } = item;
            if (!descriptionMap[objectId]) {
                descriptionMap[objectId] = {
                    objectId: objectId,
                    firstActionType: actionType,
                    lastActionType: actionType,
                    actionTypes: [actionType], // Initialize array with current actionType
                    lastAssignedToId: assignedToId, // Initialize with current assignedToId
                    type: item.applicationDetails?.category,
                    lastStatus: status, // Initialize with current status
                    data: []
                };
            } else {
                // Update lastActionType with current actionType
                descriptionMap[objectId].lastActionType = actionType;
                // Add current actionType to the array
                descriptionMap[objectId].actionTypes.push(actionType);
                // Update lastAssignedToId with current assignedToId
                descriptionMap[objectId].lastAssignedToId = assignedToId;
                // Update lastStatus with current status
                descriptionMap[objectId].lastStatus = status;
                descriptionMap[objectId].type = item.applicationDetails?.category;

            }
            descriptionMap[objectId].data.push(item);
        });

        // Update lastActionType, lastAssignedToId, and lastStatus in each group
        for (const description in descriptionMap) {
            const group = descriptionMap[description];
            const lastDataObject = group.data[group.data.length - 1]; // Get the last data object
            group.lastActionType = lastDataObject.actionType; // Update lastActionType
            group.lastAssignedToId = lastDataObject.assignedToId; // Update lastAssignedToId
            group.lastStatus = lastDataObject.status; // Update lastStatus
            group.type = lastDataObject.applicationDetails?.category;
            groupedData.push(group);
        }


        return groupedData.filter(item => item.lastActionType !== 'verify_investigation_actions' && item.type === "Non-Conformances");
    }

    const actionBodyTemplate = (rowData) => {

        setCurrent(rowData)

        const totalActionData = groupByDescription(rowData.auditData.totalActions)

        console.log(totalActionData)

        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'approve' && item.lastStatus === 'submitted')

        const color = totalActionData.length === totalCompleted.length ? 'greenBox' : totalCompleted.length === 0 ? 'redBox' : 'orangeBox';




        // Return the link with dynamic styles and counts
        return <a href="#" onClick={(e) => { e.preventDefault(); handleAuditActionCard(rowData.maskId, totalActionData) }} className={color} > {totalCompleted.length} / {totalActionData.length}</a>;
    }

    const handleAuditActionCard = (id, actions) => {

        console.log(actions)
        setAuditActionModal(true)

        setMaskId(id)
        setTotalAction(actions)
    }

    const checklistFilterTemplate = (options) => {

        return (

            <React.Fragment>
                <MultiSelect value={options.value} options={[{ name: 'GMS Assurance Audit', value: 'GMS Assurance Audit' }, { name: 'EHS Management System Audit', value: 'EHS Management System Audit' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>

        );
    }
    const locationFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <MultiSelect value={options.value} options={location} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    return (
        <>


            <div>

                <DataTable
                    value={filterData}
                    paginator
                    rows={10}
                    filters={filters}
                  
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                    rowsPerPageOptions={[10, 25, 50]}
                    emptyMessage="No Data found."
                    globalFilterFields={['maskId', 'checklist.name', 'locationFour.name', 'dateTime', 'endDateTime', 'assignedTo.firstName', 'status']}
                >

                    <Column field='maskId' body={maskIdBodyTemplate} header="Audit ID" sortable></Column>
                    <Column field='checklist.name' header='Audit Type' filter filterElement={checklistFilterTemplate} showFilterMatchModes={false}></Column>
                    <Column field='locationFour.name' header="Project / DC / Office" filter filterElement={locationFilterTemplate} showFilterMatchModes={false}></Column>
                    <Column field='dateTime' body={dateFromFormat} header="From" ></Column>
                    <Column field='endDateTime' body={dateToFormat} header="To"></Column>
                    <Column field='assignedTo.firstName' header="Assigned Auditor" filter filterElement={assignedFilterTemplate} showFilterMatchModes={false}></Column>
                    <Column field='status' header="Status of Non-conformances" body={actionBodyTemplate} ></Column>
                    {/* <Column header="Action" body={viewTemplate} showFilterMatchModes={false}></Column> */}

                </DataTable>



            </div>

            {selectedAudit && <Modal
                show={showModal}
                size="lg"
                onHide={() => setActionModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <AuditPanel readOnly={true} auditModal={showModal} setAuditModal={setShowModal} auditId={selectedAudit} />

                </Modal.Body>

            </Modal>}

            {selectedAudit && <Modal
                show={auditFindingModal}
                size="lg"
                onHide={() => setAuditFindingModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <AuditFindingPanel auditId={selectedAudit} />

                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setAuditFindingModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>}

            {selectedAudit && <Modal
                show={auditModal}
                size="lg"
                onHide={() => setActionModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <AuditPanel auditModal={auditModal} setAuditModal={setAuditModal} auditId={selectedAudit} />

                </Modal.Body>

            </Modal>}

            <Modal
                show={auditActionModal}
                size="lg"
                onHide={() => setAuditActionModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>

                    <ActionTable totalActions={actionData} id={maskId} actions={totalAction}  current={current}/>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setAuditActionModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>

        </>
    )
}

export default AuditTable;

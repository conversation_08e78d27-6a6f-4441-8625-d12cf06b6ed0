import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    WORK_ACTIVITIES_URL,

} from '../constants';
import { createAxiosInstanceWithToken } from './TempAxios';

const WorkActivityDropdown = ({ incidentData, setIncidentData, readOnly }) => {
    const [data, setData] = useState([]);


    const [selected, setSelected] = useState(incidentData.workActivity ? incidentData.workActivity.id ?? '' : '');


    const axiosInstance = createAxiosInstanceWithToken();

    useEffect(() => {
        axiosInstance.get(WORK_ACTIVITIES_URL)
            .then(response => {
                setData(response.data);
                console.log(response.data, 'cjecl')
            })
            .catch(error => {
                console.error('Error fetching work activity', error);
            });
    }, []);


    return (
        <div className=''>

            <div className='form-group'>
                <label className=''> Workplace Activity <span style={{ color: 'red' }}>*</span></label>
                <select className="form-select me-2" disabled={readOnly} value={selected} onChange={(e) => { setSelected(e.target.value); setIncidentData((prev) => ({ ...prev, workActivityId: e.target.value })) }}>
                    <option value="">Select</option>
                    {data.map(i => (
                        <option key={i.id} value={i.id}>{i.title}</option>
                    ))}
                </select>


            </div>



        </div>
    );
};

WorkActivityDropdown.defaultProps = {
    readOnly: false
}

export default WorkActivityDropdown;

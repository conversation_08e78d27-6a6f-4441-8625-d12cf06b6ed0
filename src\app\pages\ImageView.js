import React, { useEffect, useState } from "react";
import { STATIC_URL } from "../constants";
import GalleryPage from "../apps/Gallery";

const ImageView = ({ photos }) => {
    const [img, setImg] = useState([]);
    const [invalidFiles, setInvalidFiles] = useState([]);

    useEffect(() => {
        if (photos) {
            const validImages = [];
            const invalidFilesList = [];

            photos.forEach((file) => {
                const extension = file.split('.').pop().toLowerCase();
                const validFormats = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
                
                if (validFormats.includes(extension)) {
                    validImages.push({ src: `${STATIC_URL}/${file}`, width: 4, height: 3 });
                } else {
                    invalidFilesList.push({
                        link: `${STATIC_URL}/${file}`,
                        name: file.split('/').pop(),
                        type: extension,
                    });
                }
            });

            setImg(validImages);
            setInvalidFiles(invalidFilesList);
        } else {
            setImg([]);
            setInvalidFiles([]);
        }
    }, [photos]);

    const getFileIcon = (type) => {
        switch (type) {
            case "pdf":
                return <i className="pi pi-file-pdf" style={{ color: "red", fontSize: "1.5em" }}></i>;
            case "doc":
            case "docx":
                return <i className="pi pi-file-word" style={{ color: "blue", fontSize: "1.5em" }}></i>;
            case "xls":
            case "xlsx":
                return <i className="pi pi-file-excel" style={{ color: "green", fontSize: "1.5em" }}></i>;
            case "txt":
                return <i className="pi pi-file" style={{ fontSize: "1.5em" }}></i>;
            default:
                return <i className="pi pi-file" style={{ fontSize: "1.5em" }}></i>;
        }
    };

    return (
        <>
            {img.length > 0 && (
                <div className="table-responsive mb-3">
                    <table className="table table-striped">
                        <tbody>
                            <tr>
                                <td><GalleryPage photos={img} /></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            )}
            {invalidFiles.length > 0 && (
                <div className="table-responsive mb-3">
                    <table className="table table-striped">
                        <tbody>
                            {invalidFiles.map((file, index) => (
                                <tr key={index}>
                                    <td>
                                        {getFileIcon(file.type)}&nbsp;
                                        <a href={file.link} target="_blank" rel="noopener noreferrer">
                                            {file.name}
                                        </a>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </>
    );
};

export default ImageView;

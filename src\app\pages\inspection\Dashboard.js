import React, { useState, useEffect } from "react";
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button as B1 } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { Tag } from 'primereact/tag';
import { FilterMatchMode } from 'primereact/api';
import moment from 'moment';

import AddInspection from "./AddInspection";

const Dashboard = ({ data }) => {
    const [inspection, setInspection] = useState([]);
    const [addModal, setAddModal] = useState(false);
    const [inspectionData, setInspectionData] = useState(null);
    const [checklist, setChecklist] = useState([]);
    const [assignee, setAssignee] = useState([]);

    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        maskId: { value: null, matchMode: FilterMatchMode.IN },
        "checklist.name": { value: null, matchMode: FilterMatchMode.IN },
        "assignedTo.firstName": { value: null, matchMode: FilterMatchMode.IN },
        statusDisplay: { value: null, matchMode: FilterMatchMode.IN },
        permitType: { value: null, matchMode: FilterMatchMode.IN },
    });

    const statusOptions = [
        { name: "Overdue", value: "Overdue" },
        { name: "Upcoming", value: "Upcoming" },
        { name: "Open", value: "Open" },
        { name: "Completed with Open Actions", value: "Done. (Open Actions)" },
        { name: "Completed with all actions closed", value: "Closed" }
    ];

    useEffect(() => {
        if (data) {
            const processedData = data.map(item => {
                const { status } = getStatusAndBadge(item);
                return {
                    ...item,
                    statusDisplay: status
                };
            });

            setInspection(processedData);

            const checklistOptions = data.map(item => ({
                name: item.checklist?.name || '',
                value: item.checklist?.name || ''
            }));
            setChecklist(uniqueOptions(checklistOptions));

            const assigneeOptions = data.map(item => ({
                name: item.assignedTo?.firstName || '',
                value: item.assignedTo?.firstName || ''
            }));
            setAssignee(uniqueOptions(assigneeOptions));
        }
    }, [data]);

    const uniqueOptions = (arr) => {
        return arr.filter((ele, idx, self) =>
            idx === self.findIndex(e => e.value === ele.value)
        );
    };

    // Helper function to group actions by description
    function groupByDescription(data) {
        if (!data || !Array.isArray(data)) return [];

        const filterData = data.filter(item => item.actionType !== 'inspect');
        const groupedData = [];
        const descriptionMap = {};

        filterData.forEach(item => {
            const { objectId, description, actionType, assignedToId, status } = item;
            if (!descriptionMap[description]) {
                descriptionMap[description] = {
                    objectId: objectId,
                    firstActionType: actionType,
                    lastActionType: actionType,
                    actionTypes: [actionType],
                    lastAssignedToId: assignedToId,
                    lastStatus: status,
                    data: []
                };
            } else {
                descriptionMap[description].lastActionType = actionType;
                descriptionMap[description].actionTypes.push(actionType);
                descriptionMap[description].lastAssignedToId = assignedToId;
                descriptionMap[description].lastStatus = status;
            }
            descriptionMap[description].data.push(item);
        });

        // Update lastActionType, lastAssignedToId, and lastStatus in each group
        for (const description in descriptionMap) {
            const group = descriptionMap[description];
            const lastDataObject = group.data[group.data.length - 1];
            group.lastActionType = lastDataObject.actionType;
            group.lastAssignedToId = lastDataObject.assignedToId;
            group.lastStatus = lastDataObject.status;
            groupedData.push(group);
        }

        return groupedData;
    }

    const getStatusAndBadge = (item) => {
        // First check if we need to determine status based on action completion ratio
        if (item.inspectionData && item.inspectionData.totalActions) {
            const totalActionData = groupByDescription(item.inspectionData.totalActions);
            const totalCompleted = totalActionData.filter(action =>
                action.lastActionType === 'approve' && action.lastStatus === 'submitted'
            );

            // If ratio is 3/3 (all actions completed)
            if (totalActionData.length > 0 && totalActionData.length === totalCompleted.length) {
                return { status: "Closed", severity: "success" }; // Maps to "Completed with all actions closed"
            }
            // If ratio is 2/3 (some actions completed)
            else if (totalCompleted.length > 0 && totalCompleted.length < totalActionData.length) {
                return { status: "Done. (Open Actions)", severity: "success" }; // Maps to "Completed with Open Actions"
            }
        }

        // Handle special status values
        if (item.status) {
            if (item.status === "Done. (Open Actions)") {
                return { status: "Done. (Open Actions)", severity: "success" };
            }
            if (item.status === "Closed") {
                return { status: "Closed", severity: "success" };
            }
            // For any other explicit status values
            return { status: item.status, severity: "success" };
        }

        // Keep the existing logic for date-based statuses
        const now = moment().startOf('day');
        const due = moment(item.dateTime, ["DD/MM/YYYY", "DD-MM-YYYY", moment.ISO_8601], true);
        const start = item.startDateTime
            ? moment(item.startDateTime, ["DD/MM/YYYY", "DD-MM-YYYY", moment.ISO_8601], true)
            : null;

        if (!due.isValid()) return { status: "Open", severity: "info" };

        if (due.isBefore(now)) {
            return { status: "Overdue", severity: "danger" };
        } else if ((!start || !start.isValid() || now.isBefore(start)) && now.isBefore(due)) {
            return { status: "Upcoming", severity: "warning" };
        } else {
            return { status: "Open", severity: "info" };
        }
    };

    const statusBodyTemplate = (rowData) => {
        const { status, severity } = getStatusAndBadge(rowData);
        return <Tag value={labelMap[status] || status} severity={severity} />;
    };

    const formatDate = (dateStr) => {
        if (!dateStr) return '';
        const [day, month, year] = dateStr.split('-');
        if (!day || !month || !year) return dateStr;
        const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const monthIdx = parseInt(month, 10) - 1;
        return `${day} ${monthNames[monthIdx]} ${year}`;
    };

    const checklistFilterTemplate = (options) => (
        <MultiSelect value={options.value} options={checklist} itemTemplate={optionItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
    );

    const assigneeFilterTemplate = (options) => (
        <MultiSelect value={options.value} options={assignee} itemTemplate={optionItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
    );

    const statusFilterTemplate = (options) => (
        <MultiSelect value={options.value} options={statusOptions} itemTemplate={optionItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
    );

    const labelMap = {
        "Done. (Open Actions)": "Completed with Open Actions",
        "Closed": "Completed with all actions closed"
    };

    const optionItemTemplate = (option) => (
        <div className="flex align-items-center gap-2">
            <span>{labelMap[option.value] || option.value}</span>
        </div>
    );

    const maskIdBodyTemplate = (row) => {
        const isClickable = row.statusDisplay === 'Upcoming' || row.statusDisplay === 'Open';

        if (isClickable) {
            return (
                <div
                    className="maskid"
                    onClick={() => { setInspectionData(row); setAddModal(true); }}
                    style={{ cursor: 'pointer', textDecoration: 'underline' }}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            setInspectionData(row);
                            setAddModal(true);
                        }
                    }}
                >
                    {row.maskId}
                </div>
            );
        }

        return <span>{row.maskId}</span>;
    };


    const locationBodyTemplate = (rowData) => ` ${rowData.locationFour?.name || ''}`;
    const assignedByBodyTemplate = (rowData) => rowData.assignedBy?.firstName || '';

    const headerTemplate = () => (
        <div className='d-flex justify-content-end align-items-end'>
            <B1 label="Add New" icon="pi pi-plus" className="btn btn-primary" onClick={() => { setAddModal(true); setInspectionData(null); }} />
        </div>
    );

    return (
        <>
            <div className="table-container">
                <DataTable
                    value={inspection}
                    filters={filters}
                    header={headerTemplate}
                    paginator
                    rows={10}
                    rowsPerPageOptions={[5, 10, 20]}
                >
                    <Column field="statusDisplay" header="Status" body={statusBodyTemplate} filter filterElement={statusFilterTemplate} showFilterMatchModes={false} style={{ width: '180px' }} />
                    <Column field="maskId" header="ID" body={maskIdBodyTemplate} />
                    <Column field="location" header="Location" body={locationBodyTemplate} />
                    <Column field="startDateTime" header="Inspection Start Date" body={(rowData) => formatDate(rowData.startDateTime)} />
                    <Column field="checklist.name" header="Checklist Name" filter filterElement={checklistFilterTemplate} showFilterMatchModes={false} />
                    <Column field="dateTime" header="Due Date" body={(rowData) => formatDate(rowData.dateTime)} />
                    <Column field="assignedTo.firstName" header="Assignee" filter filterElement={assigneeFilterTemplate} showFilterMatchModes={false} />
                    <Column field="assignedBy.firstName" header="Assigned By" body={assignedByBodyTemplate} />
                </DataTable>
            </div>

            <AddInspection show={addModal} handleClose={() => setAddModal(false)} inspectionData={inspectionData} />
        </>
    );
};

export default Dashboard;

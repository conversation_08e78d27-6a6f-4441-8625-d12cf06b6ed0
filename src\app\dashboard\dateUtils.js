import moment from 'moment';

/**
 * Formats a date range into a human-readable string.
 * 
 * @param {Array} dateRange - Array containing two Date objects.
 * @returns {String} Formatted date range string.
 */
export const getDisplayDateRange = (dateRange) => {
  const [fromDate, toDate] = dateRange;
  
  const fromMoment = moment(fromDate);
  const toMoment = moment(toDate);

  if (fromMoment.isSame(toMoment, 'month') && fromMoment.isSame(toMoment, 'year')) {
    // Same month and year
    return fromMoment.format('MMM YYYY');
  } else if (fromMoment.isSame(toMoment, 'year')) {
    // Same year, different months
    return `${fromMoment.format('MMM')} - ${toMoment.format('MMM YYYY')}`;
  } else {
    // Different years
    return `${fromMoment.format('MMM YYYY')} - ${toMoment.format('MMM YYYY')}`;
  }
};

import heic2any from 'heic2any';

/**
 * Converts HEIC files to JPEG format with compression
 * @param {File} file - The HEIC file to convert
 * @param {number} quality - Compression quality (0.1 to 1.0)
 * @returns {Promise<File>} - The converted JPEG file
 */
export const convertHeicToJpeg = async (file, quality = 0.8) => {
  try {
    // Convert HEIC to blob
    const convertedBlob = await heic2any({
      blob: file,
      toType: 'image/jpeg',
      quality: quality
    });

    // Create a new File object from the converted blob
    const convertedFile = new File(
      [convertedBlob],
      file.name.replace(/\.heic$/i, '.jpg'),
      {
        type: 'image/jpeg',
        lastModified: Date.now()
      }
    );

    return convertedFile;
  } catch (error) {
    console.error('Error converting HEIC to JPEG:', error);
    throw new Error('Failed to convert HEIC image. Please try again.');
  }
};

/**
 * Checks if a file is in HEIC format
 * @param {File} file - The file to check
 * @returns {boolean} - True if the file is HEIC format
 */
export const isHeicFile = (file) => {
  return file.type === 'image/heic' || 
         file.type === 'image/heif' || 
         file.name.toLowerCase().endsWith('.heic') || 
         file.name.toLowerCase().endsWith('.heif');
};

/**
 * Processes uploaded files, converting HEIC files to JPEG
 * @param {FileList|Array} files - The files to process
 * @param {number} quality - Compression quality for HEIC conversion (0.1 to 1.0)
 * @returns {Promise<Array>} - Array of processed files
 */
export const processUploadedFiles = async (files, quality = 0.8) => {
  const fileArray = Array.from(files);
  const processedFiles = [];

  for (const file of fileArray) {
    try {
      if (isHeicFile(file)) {
        console.log(`Converting HEIC file: ${file.name}`);
        const convertedFile = await convertHeicToJpeg(file, quality);
        processedFiles.push(convertedFile);
      } else {
        processedFiles.push(file);
      }
    } catch (error) {
      console.error(`Error processing file ${file.name}:`, error);
      // You might want to show a user-friendly error message here
      throw error;
    }
  }

  return processedFiles;
};

/**
 * Gets the accepted file types including HEIC
 * @returns {string} - Comma-separated list of accepted file types
 */
export const getAcceptedFileTypes = () => {
  return 'image/jpeg,image/jpg,image/png,image/heic,image/heif,.heic,.heif';
};

/**
 * Validates file type (including HEIC support)
 * @param {File} file - The file to validate
 * @returns {boolean} - True if file type is supported
 */
export const isValidImageFile = (file) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif'];
  const validExtensions = ['.jpg', '.jpeg', '.png', '.heic', '.heif'];
  
  return validTypes.includes(file.type) || 
         validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
};

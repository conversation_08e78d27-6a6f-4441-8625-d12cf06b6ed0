// @ts-nocheck
import React, { useState, useRef, useEffect } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { EHS_ROLE_URL, EPTW_ROLE_URL, EXTERNAL_USERS_URL, GET_USER_LOCATION_ROLE_URL, INCIDENT_ROLE_URL, INSPECTION_ROLE_URL, INTERNAL_USERS_URL, LOCATION1_URL, PLANT_ROLE_URL, UPDATE_BULK_USER_URL, USERS_URL, USERS_URL_WITH_ID, USER_LOCATION_ROLE_URL, USER_LOCATION_URL } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import UserSelection from './UserSelection';
import FilterLocation from './FilterLocation';
import UserSelect from './UserSelect';
import Select from 'react-select';
import AllFilterLocation from './AllFilterLocation';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const ProjectUser = () => {

    const [allUsers, setAllUsers] = useState([]);
    const [selectedUsersList, setSelectedUsersList] = useState([]);
    const [filterData, setFilterData] = useState([]);
    const [selectedRoles, setSelectedRoles] = useState([]);
    const [selectedOption, setSelectedOption] = useState(null);


    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
    function updateUserSignal() {
        getAllUsers();
    }


    function handleRoleSelect(selectedOptions) {
        setSelectedRoles(selectedOptions);
    }

    useEffect(() => {
        getEhsRole();
        getEptwRole();
        getIncidentRole();
        getInspectionRole();
        getPlantRole();

        getAllUsers();

    }, [])




    const getAllUsers = async () => {
        const response = await API.get(USERS_URL)

        if (response.status === 200) {

            setAllUsers(response.data.filter(i => i.status !== false).sort((a, b) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase())))

        }
    }

    const getEhsRole = async () => {
        const response = await API.get(EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, ehs: response.data } })
        }
    }

    const getEptwRole = async () => {
        const response = await API.get(EPTW_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, eptw: response.data } })
        }
    }

    const getIncidentRole = async () => {
        const response = await API.get(INCIDENT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, incident: response.data } })
        }
    }

    const getInspectionRole = async () => {
        const response = await API.get(INSPECTION_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, inspection: response.data } })
        }
    }

    const getPlantRole = async () => {
        const response = await API.get(PLANT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, plant: response.data } })
        }
    }


    const [selectedLocationOne, setSelectedLocationOne] = useState('')
    const [selectedLocationTwo, setSelectedLocationTwo] = useState('')
    const [selectedLocationThree, setSelectedLocationThree] = useState('')
    const [selectedLocationFour, setSelectedLocationFour] = useState('')

    const [newSignal, setNewSignal] = useState(0)
    useEffect(() => {
        if (selectedLocationOne && selectedRoles) {

            getSelectedUsers();

            // Do something with the filteredUsers array
            // console.log(filteredUsers);
            
        }
        setNewSignal(prev => prev + 1)
        updateUserSignal()
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedRoles])

    const getSelectedUsers = async () => {
        const locations = {
            locationOne: selectedLocationOne,
            locationTwo: selectedLocationTwo,
            locationThree: selectedLocationThree,
            locationFour: selectedLocationFour
        }
        const modifiedSelectedRoles = selectedRoles.map(i => i.value)
        const response = await API.post(GET_USER_LOCATION_ROLE_URL, { roles: modifiedSelectedRoles, locations: locations })

        if (response.status === 200) {

            setSelectedUsersList(response.data); 
        }
    }
    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        // const filteredData = data.filter(item => {
        //   return (
        //     (locationOneId === '' || item.locationOneId === locationOneId) &&
        //     (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
        //     (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
        //     (locationFourId === '' || item.locationFourId === locationFourId)
        //   );
        // });

        // setFilterData(filteredData);
        setSelectedLocationOne(locationOneId)
        setSelectedLocationTwo(locationTwoId)
        setSelectedLocationThree(locationThreeId)
        setSelectedLocationFour(locationFourId)
    };

    const handleSelectedUsers = async (users, deselectedUsers) => {
        if (selectedLocationOne) {

            const userIds = users.map(i => { return i.id });
            const locations = {
                locationOne: selectedLocationOne,
                locationTwo: selectedLocationTwo,
                locationThree: selectedLocationThree,
                locationFour: selectedLocationFour
            }
            const modifiedSelectedRoles = selectedRoles.map(i => i.value)
            const response = await API.post(USER_LOCATION_ROLE_URL, { userIds: userIds, deselectUserIds: deselectedUsers, roles: modifiedSelectedRoles, locations: locations })
            // console.log(response)
            if (response.status === 200) {
                cogoToast.success('Assigned')
            }
        } else {
            cogoToast.error('Please select any country to continue!')
        }
        // console.log(users)
    }
    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body">

                                <h4 className="card-title">Project Based User Assignment</h4>
                                <div className="row">
                                    <div className="col-12">

                                        <div className='mb-4'>
                                            <AllFilterLocation handleFilter={handleFilter} disableAll={true} period={false} />
                                        </div>

                                        {(selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour) && <div className='mb-4'>
                                            <label className='mb-4'>Choose Role</label>
                                            <Select
                                                className="w-25"
                                                value={selectedRoles}
                                                onChange={handleRoleSelect}
                                                options={Object.entries(allRoles)
                                                    .filter(([key]) => key !== 'country')
                                                    .map(([key, options]) => ({
                                                        label: key === 'ehs' ? 'Observation' : key,
                                                        options: options.map(option => ({
                                                            value: option.id,
                                                            label: option.name
                                                        }))
                                                    }))}
                                                isMulti
                                                placeholder="Select Roles to Assign"
                                            />
                                        </div>}

                                        <div className='row'>
                                            {(selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour) && <div className='col-6'>
                                                <h5>Select users</h5>
                                                <UserSelect newSignal={newSignal} allUsers={allUsers} selectedUsersList={selectedUsersList} handleSelectedUsers={handleSelectedUsers} />

                                            </div>}
                                            {/* <div className='col-6'>

                                                <h3>Selected Users:</h3>
                                                <ul>
                                                    {Object.entries(roleUsersMap).map(([role, users]) => (
                                                        <li key={role}>
                                                            <h4>{findSelectedRole(role)?.name}:</h4>
                                                            <ul>
                                                                {users.map((user) => (
                                                                    <li key={user.id}>
                                                                        {user.firstName} ({user.email})
                                                                    </li>
                                                                ))}
                                                            </ul>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div> */}
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



        </>
    )
}


export default ProjectUser;

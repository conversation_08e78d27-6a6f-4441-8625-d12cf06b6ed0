import React, {useRef} from "react";
import { Form } from "react-bootstrap";



const AddItem = (props) => {
    const title = useRef();

    const handleCreateItem = (e) => {
        e.preventDefault();
        // @ts-ignore
        props.onCreate(title.current.value);
        // @ts-ignore
        title.current.value = '';
    }
    return (
        <>  
            <Form.Group className="form-group mb-0">
                <div className="input-group">
                    <Form.Control type="text" ref={title} className="form-control mb-0" placeholder={props.placeholder} aria-label="item" aria-describedby="basic-addon2" />
                    <div className="input-group-append">
                        <button className="btn btn-primary btn-icon mb-0 h-100" type="button" onClick={(e) => { handleCreateItem(e) }}><i className="mdi mdi-plus-circle"></i></button>
                    </div>
                </div>
            </Form.Group>
        </>
    )
}

export default AddItem;
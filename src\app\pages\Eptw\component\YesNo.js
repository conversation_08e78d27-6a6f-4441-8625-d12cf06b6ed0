import React, { useState, useEffect } from 'react'
import { Container, Row, Col, Card, Form, InputGroup, Modal, Button, Image } from "react-bootstrap";
function YesNo({  title, value,checkYesNo,type,index,i }) {
  
console.log(value)

    return (
        <>

            <Col className="m-auto mb-3 " xs={12} sm={12} md={12}>
                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                    <Card.Body >
                        <Row >
                            <Col xs={12} sm={12} md={12} className="d-flex ">
                                <label>{title}  <span style={{ color: '#D62828' }}>*</span></label>
                            </Col>
                            <Col xs={12} sm={12} md={12} className="d-flex  text-center mt-3">
                                <Container fluid className="col-5 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }} onClick={() => checkYesNo('Yes',type,index,i)}>
                                    <Row   >
                                        <Col xs={4} sm={4} md={4}>
                                            <div style={value === 'Yes' ? { width: 24, height: 24, borderRadius: 12, background: 'green' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                {value === 'Yes' ?
                                                    <span class="material-icons" style={value === 'Yes' ? { color: 'white' } : {}}>
                                                        done
                                                    </span>
                                                    : ''}
                                            </div>
                                        </Col>
                                        <Col xs={6} sm={6} md={6} style={value === 'Yes' ? { color: 'green' } : {}}>
                                            Yes
                                        </Col>
                                    </Row>


                                </Container>
                                <Container fluid className="col-5 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }} onClick={() => checkYesNo('No',type,index,i)}>
                                    <Row   >
                                        <Col xs={4} sm={4} md={4}>
                                            <div style={value === 'No' ? { width: 24, height: 24, borderRadius: 12, background: 'red' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                {value === 'No' ?
                                                    <span class="material-icons" style={value === 'No' ? { color: 'white' } : {}}>
                                                        done
                                                    </span>
                                                    : ''}
                                            </div>
                                        </Col>
                                        <Col xs={8} sm={8} md={8} style={value === 'No' ? { color: 'red' } : {}}>
                                            No
                                        </Col>
                                    </Row>


                                </Container>

                            </Col>
                        </Row>



                    </Card.Body>

                </Card>
            </Col>

        </>

    )
}

export default YesNo
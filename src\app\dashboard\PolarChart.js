import React from "react";
import Chart from 'react-apexcharts';

const PolarChart = (props) => {
    const options = {
        chart: {
            type: 'polarArea',
            toolbar: {
                show: true
            },
            zoom: {
                enabled: true
            },
            height: 350
        },
        stroke: {
            colors: ['#fff']
        },
        fill: {
            opacity: 0.8
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }],
        labels: ['DC', 'Tower Crane', 'High Risk'], // Added labels
    }

    const series = [14, 23, 21]

    return (
        <>
            <Chart
                options={options}
                series={series}
                type="polarArea"
                height="400"
            />
        </>
    )
}

export default PolarChart;

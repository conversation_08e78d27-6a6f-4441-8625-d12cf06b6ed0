import React, { useState, useEffect } from "react";
import moment from "moment";

function HorizontalStackedBarChart({ info, daterange }) {
    const [matrixData, setMatrixData] = useState([]);
    const [rawData, setRawData] = useState({
        totalMonths: 12,
        topMonthlyGhs: {
            "2023-11": [{ name: "GMS 4.6 Fire and Explosion", count: 1 }],
            "2024-02": [
                { name: "GMS 4.5 Management of Hazardous Energy", count: 6 },
                { name: "GMS 4.7 Lifting and Hoisting Equipment", count: 2 },
                { name: "GMS 4.6 Fire and Explosion", count: 1 },
                { name: "GMS 4.2 Fall Prevention – Persons", count: 1 },
            ],
            "2024-03": [
                { name: "GMS 3.1 Establishing Projects & Operations", count: 5 },
                { name: "GMS 4.2 Fall Prevention – Persons", count: 3 },
                { name: "GMS 4.11 Machines With Moving Parts", count: 1 },
                { name: "GMS 4.8 Ground Disturbance", count: 1 },
                { name: "GMS 4.5 Management of Hazardous Energy", count: 1 },
            ],
            "2024-06": [
                { name: "GMS 4.1 Establishing Projects", count: 64 },
                { name: "GMS 4.5 Management of Hazardous Energy", count: 24 },
                { name: "GMS 4.3 Fall Prevention", count: 23 },
                { name: "GMS 4.11 Moving Parts", count: 21 },
                { name: "GMS 2.2 Ground Disturbance", count: 18 },
            ],
            "2024-07": [
                { name: "GMS 4.11 Temporary Fixtures", count: 50 },
                { name: "GMS 4.1 Establishing Projects", count: 43 },
            ],
        },
    });

    useEffect(() => {
        prepareMatrixData();
    }, [rawData]);

    useEffect(() => {
        if (info) setRawData(info);
    }, [info]);

    const prepareMatrixData = () => {
        const months = Object.keys(rawData.topMonthlyGhs);
        const matrix = months.map((month) => {
            const top5 = rawData.topMonthlyGhs[month]
                .sort((a, b) => b.count - a.count) // Sort by count descending
                .slice(0, 5); // Get top 5 items

            return {
                month: month,  // Keep raw 'YYYY-MM' for internal processing
                top5: top5.map((item) => item.name.split(" ")[1] || item.name), // Extract GMS code
            };
        });
        setMatrixData(matrix);
    };

    return (
        <div style={{ width: "100%", margin: "auto" }}>
            <table width={'100%'} border="1" cellPadding="10" style={{ borderCollapse: "collapse", textAlign: "center" }}>
                <thead>
                    <tr>
                        <th>Month</th>
                        {[1, 2, 3, 4, 5].map((num) => (
                            <th key={num}>{num}</th>
                        ))}
                    </tr>
                </thead>
                <tbody>
                    {matrixData.map((row) => (
                        <tr key={row.month}>
                            {/* Format month using moment */}
                            <td>{moment(row.month, "YYYY-MM").format("MMM YYYY")}</td>

                            {row.top5.map((item, index) => (
                                <td key={index}>{item}</td>
                            ))}
                            {/* Fill remaining empty cells if less than 5 GMS categories */}
                            {[...Array(5 - row.top5.length)].map((_, i) => (
                                <td key={`empty-${i}`}></td>
                            ))}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}

export default HorizontalStackedBarChart;

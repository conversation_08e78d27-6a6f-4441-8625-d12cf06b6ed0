import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import moment from "moment";

const GroupedBarChart = ({ info }) => {
    const [monthlyData, setMonthlyData] = useState({
        totalMonths: 6,
        monthlyTypeConditionCounts: {
            "2024-07": {
                "Safe (Condition)": 14,
                "Safe (Act)": 2,
                "At Risk (Act)": 2,
                "At Risk (Condition)": 16,
            },
            "2024-08": {
                "At Risk (Condition)": 143,
                "At Risk (Act)": 31,
                "Safe (Condition)": 24,
                "Safe (Act)": 26,
            },
            "2024-09": {
                "Safe (Condition)": 26,
                "Safe (Act)": 29,
                "At Risk (Condition)": 148,
                "At Risk (Act)": 31,
            },
            "2024-10": {
                "Safe (Condition)": 19,
                "Safe (Act)": 17,
                "At Risk (Condition)": 64,
                "At Risk (Act)": 21,
            },
            "2024-11": {
                "At Risk (Act)": 3,
                "Safe (Condition)": 2,
            },
            "2024-12": {
                "At Risk (Act)": 2,
            },
        },
    });

    useEffect(() => {
        if (info) {
            setMonthlyData(info);
        }
    }, [info]);

    // Generate last 12 months in 'YYYY-MM' format for data lookup
    const last12MonthsRaw = Array.from({ length: 12 }, (_, i) =>
        moment().subtract(11 - i, "months").format("YYYY-MM")
    );

    // Format labels as 'Jan 2024', 'Feb 2024', etc.
    const last12MonthsFormatted = last12MonthsRaw.map(date =>
        moment(date, "YYYY-MM").format("MMM YYYY")
    );

    // Prepare chart data with missing months filled
    const chartData = last12MonthsRaw.map((month) => {
        return monthlyData.monthlyTypeConditionCounts[month] || {};
    });

    const getTypeCounts = (type) =>
        chartData.map((monthData) => monthData[type] || 0);

    // Highcharts Configuration for Grouped Bar Chart
    const options = {
        chart: {
            type: "column",
            zoomType: "xy", // 🔹 Enables zooming (drag to zoom)
        },
        title: {
            text: "",
        },
        xAxis: {
            categories: last12MonthsFormatted,
            title: {
                text: "Month-Year",
            },
            crosshair: true,
        },
        yAxis: {
            min: 0,
            title: {
                text: "Number of Observations",
            },
        },
        tooltip: {
            shared: true,
            pointFormat: "<b>{series.name}</b>: {point.y}<br/>",
        },
        plotOptions: {
            column: {
                grouping: true, // 🔹 Groups bars instead of stacking
                dataLabels: {
                    enabled: true,
                },
            },
        },
        legend: {
            enabled: true,
        },
        series: [
            {
                name: "Safe (Condition)",
                data: getTypeCounts("Safe (Condition)"),
            },
            {
                name: "Safe (Act)",
                data: getTypeCounts("Safe (Act)"),
            },
            {
                name: "At Risk (Condition)",
                data: getTypeCounts("At Risk (Condition)"),
            },
            {
                name: "At Risk (Act)",
                data: getTypeCounts("At Risk (Act)"),
            },
        ],
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    menuItems: [
                        "downloadPNG",
                        "downloadJPEG",
                        "downloadPDF",
                        "downloadSVG",
                        "separator",
                        "downloadCSV",
                        "downloadXLS",
                    ],
                },
            },
        },
    };

    return (
        <div style={{ width: "100%", margin: "auto" }}>
            {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
        </div>
    );
};

export default GroupedBarChart;

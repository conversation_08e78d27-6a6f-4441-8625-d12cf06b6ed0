import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Dropdown } from 'primereact/dropdown';
import { InputTextarea } from 'primereact/inputtextarea';
import { Calendar } from 'primereact/calendar';
import { FileUpload } from 'primereact/fileupload';
import { Button } from 'primereact/button';
import { RadioButton } from 'primereact/radiobutton';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'primereact/resources/themes/saga-blue/theme.css';  // PrimeReact theme
import 'primereact/resources/primereact.min.css';          // Core PrimeReact CSS
import 'primeicons/primeicons.css';
import { LOCATION1_URL, LOCATION2_URL, LOCATION3_URL, LOCATION4_URL, WORK_ACTIVITIES_URL, TIER3_TIER4_URL, TIER4_TIER5_URL, ALL_LOCATION1_URL } from '../constants';
import API from '../services/API';
// import LocationSelector from './LocationSelector';

const PublicObs = () => {
    // Access the id and mode from the URL
    const { id, mode } = useParams();
    const [countries, setCountries] = useState([]);
    const [selectedCountry, setSelectedCountry] = useState(null);

    const [cities, setCities] = useState([]);
    const [selectedCity, setSelectedCity] = useState(null);

    const [businessUnits, setBusinessUnits] = useState([]);
    const [selectedBusinessUnit, setSelectedBusinessUnit] = useState(null);

    const [projects, setProjects] = useState([]);
    const [selectedProjectData, setSelectedProjectData] = useState(null);

    const [levels, setLevels] = useState([]);
    const [selectedLevel, setSelectedLevel] = useState(null);

    const [zones, setZones] = useState([]);
    const [selectedZone, setSelectedZone] = useState(null);

    const [category, setCategory] = useState('Environment');
    const [observationType, setObservationType] = useState('Safe');
    const [observationCondition, setObservationCondition] = useState('Act');
    const [rectifiedOnSpot, setRectifiedOnSpot] = useState(false);
    const [workActivities, setWorkActivities] = useState([]);
    const [selectedWorkActivity, setSelectedWorkActivity] = useState(null);

    const [safeDescription, setSafeDescription] = useState('');
    const [generalCondition, setGeneralCondition] = useState('');
    const [atRiskDescription, setAtRiskDescription] = useState('');
    const [actionsToBeTaken, setActionsToBeTaken] = useState('');
    const [actionTakenDescription, setActionTakenDescription] = useState('');
    const [actionAssignedTo, setActionAssignedTo] = useState(null);
    const [dueDate, setDueDate] = useState(null);
    const [role, setRole] = useState(null);

    useEffect(() => {
        // Start the tier-based fetch process from the selected project (which has locationThreeId)
        if (id) {

            getAllLocations(id)
        }
    }, [id]);

    const getAllLocations = async (id) => {
        const responseOne = await API.get(ALL_LOCATION1_URL);
        const mappedLocationOne = responseOne.data.map((activity) => ({
            label: activity.name, // Assuming the response contains "name" as the key
            value: activity.id,   // Assuming the response contains "id" as the key
        }));
        setCountries(mappedLocationOne)

        const responseTwo = await API.get(LOCATION2_URL);
        const mappedLocationTwo = responseTwo.data.map((activity) => ({
            label: activity.name, // Assuming the response contains "name" as the key
            value: activity.id,   // Assuming the response contains "id" as the key
        }));
        setCities(mappedLocationTwo)

        const responseThree = await API.get(LOCATION3_URL);
        const mappedLocationThree = responseThree.data.map((activity) => ({
            label: activity.name, // Assuming the response contains "name" as the key
            value: activity.id,   // Assuming the response contains "id" as the key
        }));
        setBusinessUnits(mappedLocationThree)

        const responseFour = await API.get(LOCATION4_URL);
        const mappedLocationFour = responseFour.data.map((activity) => ({
            label: activity.name, // Assuming the response contains "name" as the key
            value: activity.id,   // Assuming the response contains "id" as the key
        }));
        setProjects(mappedLocationFour)
        setSelectedProjectData(id)
        const selectedProjectDetails = responseFour.data.find(project => project.id === id);
        const selectedBUDetails = responseThree.data.find(bu => bu.id === selectedProjectDetails.locationThreeId)
        const selectedCityDetails = responseTwo.data.find(city => city.id === selectedBUDetails.locationTwoId)
        const selectedCountryDetails = responseOne.data.find(country => country.id === selectedCityDetails.locationOneId)

        setSelectedBusinessUnit(selectedBUDetails.id)
        setSelectedCity(selectedCityDetails.id)
        setSelectedCountry(selectedCountryDetails.id)
    }

    useEffect(() => {
        if (selectedProjectData) {
            fetchLevels(selectedProjectData);
        }
    }, [selectedProjectData]);

    // Trigger when a level is selected to load zones (Tier 4 -> Tier 5)
    useEffect(() => {
        if (selectedLevel) {
            fetchZones(selectedLevel);
        }
    }, [selectedLevel]);

    const fetchLevels = async (projectId) => {
        try {
            const response = await API.get(TIER3_TIER4_URL(projectId));
            const mappedLevels = response.data.map((level) => ({
                label: level.name, // Assuming response has `name`
                value: level.id,   // Assuming response has `id`
            }));
            setLevels(mappedLevels);
        } catch (error) {
            console.error('Error fetching levels:', error);
        }
    };

    // Fetch zones based on selected level (Tier 4 -> Tier 5)
    const fetchZones = async (levelId) => {
        try {
            const response = await API.get(TIER4_TIER5_URL(levelId));
            const mappedZones = response.data.map((zone) => ({
                label: zone.name, // Assuming response has `name`
                value: zone.id,   // Assuming response has `id`
            }));
            setZones(mappedZones);
        } catch (error) {
            console.error('Error fetching zones:', error);
        }
    };

    useEffect(() => {
        // Fetching workplace activities from the API when the component mounts
        fetchWorkActivities();
    }, []);

    const fetchWorkActivities = async () => {
        try {
            const response = await API.get(WORK_ACTIVITIES_URL);
            const data = response.data;

            // Map the data to a suitable format if necessary
            const mappedWorkActivities = data.map((activity) => ({
                label: activity.title, // Assuming the response contains "name" as the key
                value: activity.id,   // Assuming the response contains "id" as the key
            }));

            setWorkActivities(mappedWorkActivities);
        } catch (error) {
            console.error('Error fetching work activities:', error);
        }
    };

    // Dropdown options for role
    const roles = [
        { label: 'STT GDC Management', value: 'STT GDC Management' },
        { label: 'Visitor', value: 'Visitor' },
        { label: 'VIRTUS Management', value: 'VIRTUS Management' },
        { label: 'Main Contractor', value: 'Main Contractor' },
        { label: 'Sub-contractor', value: 'Sub-contractor' }
    ];


    return (
        <div className="publicObs-form mt-4">
            <h2>Report Observation</h2>
            <form>

                <div className="row">
                    <div className="col-md-6 mb-3">
                        <label htmlFor="role">Country</label>
                        <Dropdown disabled className="w-100" id="role" value={selectedCountry} options={countries} onChange={(e) => setSelectedCountry(e.value)} placeholder="Select" />
                    </div>
                </div>

                <div className="row">
                    <div className="col-md-6 mb-3">
                        <label htmlFor="role">City</label>
                        <Dropdown disabled className="w-100" id="role" value={selectedCity} options={cities} onChange={(e) => setSelectedCity(e.value)} placeholder="Select" />
                    </div>
                </div>

                <div className="row">
                    <div className="col-md-6 mb-3">
                        <label htmlFor="role">Business Unit</label>
                        <Dropdown disabled className="w-100" id="role" value={selectedBusinessUnit} options={businessUnits} onChange={(e) => setSelectedBusinessUnit(e.value)} placeholder="Select" />
                    </div>
                </div>

                <div className="row">
                    <div className="col-md-6 mb-3">
                        <label htmlFor="role">Project / DC Name</label>
                        <Dropdown disabled className="w-100" id="role" value={selectedProjectData} options={projects} onChange={(e) => setSelectedProjectData(e.value)} placeholder="Select" />
                    </div>
                </div>

                <div className="row">
                    <div className="col-md-6 mb-3">
                        <label htmlFor="level">Level</label>
                        <Dropdown
                            className="w-100"
                            id="level"
                            value={selectedLevel}
                            options={levels}
                            onChange={(e) => setSelectedLevel(e.value)}
                            placeholder="Select Level"
                            disabled={!selectedProjectData} // Disabled until project is selected
                        />
                    </div>
                </div>

                {/* Zone Dropdown (Tier 4 -> Tier 5) */}
                <div className="row">
                    <div className="col-md-6 mb-3">
                        <label htmlFor="zone">Zone</label>
                        <Dropdown
                            className="w-100"
                            id="zone"
                            value={selectedZone}
                            options={zones}
                            onChange={(e) => setSelectedZone(e.value)}
                            placeholder="Select Zone"
                            disabled={!selectedLevel} // Disabled until level is selected
                        />
                    </div>
                </div>

                <div className="row">
                    <div className="col-md-6 mb-3">
                        <label htmlFor="role">Select Your Role</label>
                        <Dropdown className="w-100" id="role" value={role} options={roles} onChange={(e) => setRole(e.value)} placeholder="Select" />
                    </div>
                </div>



                <div className="row">
                    <div className="col-md-12 mb-3">
                        <label>Category</label>
                        <div className="d-flex">
                            <RadioButton inputId="category1" name="category" value="Environment" checked={category === 'Environment'} onChange={(e) => setCategory(e.value)} />
                            <label htmlFor="category1" className="ms-2">Environment</label>
                            <RadioButton inputId="category2" name="category" value="Health" checked={category === 'Health'} onChange={(e) => setCategory(e.value)} className="ms-4" />
                            <label htmlFor="category2" className="ms-2">Health</label>
                            <RadioButton inputId="category3" name="category" value="Safety" checked={category === 'Safety'} onChange={(e) => setCategory(e.value)} className="ms-4" />
                            <label htmlFor="category3" className="ms-2">Safety</label>
                        </div>
                    </div>
                    <div className="col-md-12 mb-3">
                        <label>Type of Observation</label>
                        <div className="d-flex">
                            <RadioButton inputId="observationType1" name="observationType" value="Safe" checked={observationType === 'Safe'} onChange={(e) => setObservationType(e.value)} />
                            <label htmlFor="observationType1" className="ms-2">Safe</label>
                            <RadioButton inputId="observationType2" name="observationType" value="At-Risk" checked={observationType === 'At-Risk'} onChange={(e) => setObservationType(e.value)} className="ms-4" />
                            <label htmlFor="observationType2" className="ms-2">At-Risk</label>
                        </div>
                    </div>
                    <div className="col-md-12 mb-3">

                        <div className="d-flex">
                            <RadioButton inputId="observationCondition1" name="observationCondition" value="Condition" checked={observationCondition === 'Condition'} onChange={(e) => setObservationCondition(e.value)} />
                            <label htmlFor="observationCondition1" className="ms-2">Condition</label>
                            <RadioButton inputId="observationType2" name="observationCondition" value="Act" checked={observationCondition === 'Act'} onChange={(e) => setObservationCondition(e.value)} className="ms-4" />
                            <label htmlFor="observationCondition2" className="ms-2">Act</label>


                        </div>
                    </div>
                </div>

                <div className="mb-3">
                    <label htmlFor="workplaceActivity">Workplace Activity</label>
                    <Dropdown
                        className='w-100'
                        id="workActivity"
                        value={selectedWorkActivity}
                        options={workActivities}
                        onChange={(e) => setSelectedWorkActivity(e.value)}
                        placeholder="Select Activity"
                    />
                </div>

                <div className="mb-3">
                    <label htmlFor="safeDescription">General Condition</label>
                    <InputTextarea className="w-100" id="safeDescription" value={generalCondition} onChange={(e) => setGeneralCondition(e.target.value)} rows={3} />
                </div>

                {observationType === 'Safe' && (
                    <>
                        <div className="mb-3">
                            <label htmlFor="safeDescription">Safe Observation Description</label>
                            <InputTextarea className="w-100" id="safeDescription" value={safeDescription} onChange={(e) => setSafeDescription(e.target.value)} rows={3} />
                        </div>
                        <div className="mb-3">
                            <label>Upload Images (Optional)</label>
                            <FileUpload name="demo[]" url="./upload" mode="advanced" accept="image/*" maxFileSize={50000000} chooseLabel="Choose" />
                        </div>
                    </>
                )}

                {observationType === 'At-Risk' && (
                    <>
                        <div className="mb-3">
                            <label>Rectified on the Spot?</label>
                            <div className="d-flex">
                                <RadioButton inputId="rectifiedYes" name="rectified" value={true} checked={rectifiedOnSpot === true} onChange={(e) => setRectifiedOnSpot(e.value)} />
                                <label htmlFor="rectifiedYes" className="ms-2">Yes</label>
                                <RadioButton inputId="rectifiedNo" name="rectified" value={false} checked={rectifiedOnSpot === false} onChange={(e) => setRectifiedOnSpot(e.value)} className="ms-4" />
                                <label htmlFor="rectifiedNo" className="ms-2">No</label>
                            </div>
                        </div>

                        {rectifiedOnSpot ? (
                            <>
                                <div className="mb-3">
                                    <label htmlFor="actionTakenDescription">Describe Action Taken</label>
                                    <InputTextarea className="w-100" id="actionTakenDescription" value={actionTakenDescription} onChange={(e) => setActionTakenDescription(e.target.value)} rows={3} />
                                </div>
                                <div className="mb-3">
                                    <label>Upload Images</label>
                                    <FileUpload name="demo[]" url="./upload" mode="advanced" accept="image/*" maxFileSize={50000000} chooseLabel="Choose" />
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="mb-3">
                                    <label htmlFor="atRiskDescription">Describe the At-Risk Observation</label>
                                    <InputTextarea className="w-100" id="atRiskDescription" value={atRiskDescription} onChange={(e) => setAtRiskDescription(e.target.value)} rows={3} />
                                </div>

                                {['Main Contractor', 'Sub-contractor'].includes(role) && (
                                    <>
                                        <div className="mb-3">
                                            <label htmlFor="actionsToBeTaken">Actions to be Taken</label>
                                            <InputTextarea className="w-100" id="actionsToBeTaken" value={actionsToBeTaken} onChange={(e) => setActionsToBeTaken(e.target.value)} rows={3} />
                                        </div>

                                        <div className="row">
                                            <div className="col-md-6 mb-3">
                                                <label htmlFor="actionAssignedTo">Assign Action to</label>
                                                <Dropdown className="w-100" id="actionAssignedTo" value={actionAssignedTo} options={businessUnits} onChange={(e) => setActionAssignedTo(e.value)} optionLabel="name" placeholder="Select" />
                                            </div>
                                            <div className="col-md-6 mb-3">
                                                <label htmlFor="dueDate">Due Date</label>
                                                <Calendar className="w-100" id="dueDate" value={dueDate} onChange={(e) => setDueDate(e.value)} showIcon dateFormat="dd-mm-yy" />
                                            </div>
                                        </div></>)

                                }

                                < div className="mb-3">
                                    <label>Upload Images (Optional)</label>
                                    <FileUpload name="demo[]" url="./upload" mode="advanced" accept="image/*" maxFileSize={50000000} chooseLabel="Choose" />
                                </div>
                            </>
                        )}
                    </>
                )}

                <div className="mt-4">
                    <Button label="Submit" type="submit" className="p-button-danger" />
                </div>
            </form>
        </div >
    );
};

export default PublicObs;

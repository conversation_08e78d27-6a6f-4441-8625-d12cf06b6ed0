import React, { useState, useEffect } from "react";
import CreatableSelect from 'react-select/creatable';
import ListBox from "../form-elements/ListBox";
import { EDIT_GMS_URL, LOCATION1_URL, LOCATION1_WITH_ID_URL, LOCATION_CONFIG_URL, LOCATION_TIER1_URL, TIER1_TIER2_URL, TIER2_TIER3_URL, TEST_CASE_URL, TEST_CASE_WITH_ID_URL, TIER3_TIER4_URL, CHECKLIST_URL, DOCUMENTS_URL, EDIT_TIER_URL, SUBMIT_URL, GMS1_URL, GMS1_GMS2_URL } from "../constants";

import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import LocationList from "../form-elements/LocationList";
import { deletePopup, singlePopup } from "../notifications/Swal";
import { <PERSON><PERSON> } from "react-bootstrap";
import SubmitCard from "../dashboard/SubmitCard";
import AddItem from "../form-elements/AddItem";
import API from "../services/API";

const Gms = () => {


  const [tier1, setTier1] = useState([]);
  const [selectedTier1, setSelectedTier1] = useState({ id: '' });
  const [selectedTier2, setSelectedTier2] = useState({ id: '' });

  useEffect(() => { getGMS1() }, [])

  const getGMS1 = async () => {
    const response = await API.get(GMS1_URL);
    if (response.status === 200) {
      const tier1 = response.data;
      setTier1(tier1);
    }
  }


  const handleTier1Select = (id) => {
    setSelectedTier1(tier1.find(i => i.id === id))
  }

  const createTier1 = async (value) => {
    const response = await API.post(GMS1_URL, { name: value })
    if (response.status === 200) {
      const createdTier1 = response.data;
      setTier1((prev) => [...prev, createdTier1]);
      cogoToast.info('Created!', { position: 'top-right' })

    }

  }

  const getTier1Tier2 = async () => {
    const response = await API.get(GMS1_GMS2_URL(selectedTier1.id));
    if (response.status === 200) {
      const tier2 = response.data;
      setTier2(tier2);
    }
  }

  const [tier2, setTier2] = useState([]);


  useEffect(() => {
    if (selectedTier1.id !== '')
      getTier1Tier2();


  }, [selectedTier1.id])



  const createTier2 = async (value) => {
    const response = await API.post(GMS1_GMS2_URL(selectedTier1.id), {  name: value })
    if (response.status === 200) {
      const createdTier2 = response.data;
      setTier2((prev) => [...prev, createdTier2]);
      cogoToast.info('Created!', { position: 'top-right' })
    }
  }




  const handleDeleteItem = async (mode, id) => {

    deletePopup.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
    }).then(async (result) => {
      if (result.isConfirmed) {
        //   deleteChecklist(id);

        const response = await API.delete(EDIT_GMS_URL(mode, id))

        if (response.status === 204) {
          switch (mode) {
            case 'tier1':
              setTier1(prev => prev.filter(i => i.id !== id))
              setSelectedTier1({ id: '' });
              break;
            case 'tier2':
              setTier2(prev => prev.filter(i => i.id !== id))



              break;


            default: break;
          }
          singlePopup.fire(
            'Deleted!',
            '',
            'success'
          );
        }

      }
    })

  }




  return (
    <>
      <div className="">
        <div className="col-lg-12 p-0">

          <h4 className="card-title mb-3">Configure Global Minimum Standard (GMS)</h4>



          <>
            <div className="row">
              <div className="col-4 p-1 ps-3">
                <ListBox handleDeleteItem={handleDeleteItem} selected={'true'} title={'Global Minimum Standard (GMS)'} onHandleCreateItem={createTier1} lists={tier1} handleSelect={handleTier1Select} selectedItem={selectedTier1} mode='tier1' />
              </div>
              <div className="col-4 p-1">
                <ListBox handleDeleteItem={handleDeleteItem} title={'GMS Sub Section'} onHandleCreateItem={createTier2} lists={tier2} selected={selectedTier1.id !== ''} selectedItem={selectedTier2} mode='tier2' />
              </div>


            </div>
          </>






        </div>
      </div>




    </>
  );

}

export default Gms;
import React, { useEffect, useState } from 'react'
import $ from "jquery";

import API from '../services/API';

import { REPORT_INCIDENT_URL_WITH_ID,REPORT_INCIDENT_LEAD_INVESTIGATOR_URL } from '../constants';

import IncidentInvestigationModal from './IncidentInvestigationModal';
import moment from 'moment';


// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const IncidentInvestigationCard = () => {


    const [data, setData] = useState([]);

    useEffect(() => {
        getIncidentData();
    }, [])

    const getIncidentData = async () => {
        const params = {
            "include": [{ "relation": "user" }]

        };
        const response = await API.get(`${REPORT_INCIDENT_LEAD_INVESTIGATOR_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
        if (response.status === 200) {
            setData(response.data)

        }
    }


    const [showModal, setShowModal] = useState(false)

    const [currentIncident, setCurrentIncident] = useState('')
    const viewIncident = async (id) => {
        setCurrentIncident(id);

        getReportIncident(id)
    }


    const [incidentData, setIncidentData] = useState({})
    const getReportIncident = async (id) => {

        const uriString = { include: ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationFive', 'locationSix', 'incidentCircumstanceCategory', 'incidentCircumstanceDescription', 'incidentCircumstanceType', 'lighting', 'riskCategory', 'surfaceCondition', 'surfaceType', 'workActivity'] }

        const url = `${REPORT_INCIDENT_URL_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data;
            setIncidentData(data)
            setShowModal(true)

        }
    }

    return (
        <>

            <div>
                <div>
                    {
                        data.map(i => {
                            return (
                                <div className='zoom-on-hover card-body shadow-light border-radius-5 mb-3 cursor-pointer' key={i.id}
                                    onClick={() => viewIncident(i.id)}
                                >
                                    <div className='d-flex justify-content-between'>
                                        <p># {i.maskId}</p>

                                        <p><strong>Conduct Investigation</strong></p>
                                    </div>
                                    <p>Incident Date: {moment(i.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY hh:mm A')}</p>

                                    <p>Description: {i.description}</p>
                                    <p>Category: {i.IncidentCategory}</p>

                                </div>
                            )
                        })
                    }
                </div>
            </div>


            {(currentIncident && showModal) && <IncidentInvestigationModal incidentData={incidentData} showModal={showModal} setShowModal={setShowModal} />}
        </>
    )
}

export default IncidentInvestigationCard

/* eslint-disable jsx-a11y/anchor-is-valid */
// @ts-nocheck
import React, { useState, useRef, useEffect } from "react";
import ListBoxItem from "./ListBoxItem";
import { Form, Modal, Button } from "react-bootstrap";
import Editable from "react-bootstrap-editable";
import autoAnimate from "@formkit/auto-animate";
import { dateRegex } from "../utils/Regex";
// import { EDIT_TIER_URL, TEST_CASE_URL, TEST_CASE_WITH_ID_URL, NEW_TEST_CASE_WITH_ID_URL, NEW_TEST_CASE_URL } from "../../constants";
import DatePicker from "react-datepicker";
import Select from 'react-select';
import moment from 'moment';
import API from "../services/API";
import { EQUIPMENT_WITH_ID_URL } from "../constants";
import cogoToast from "cogo-toast";
import { v4 as uuidv4 } from 'uuid';
import { Accordion } from 'react-bootstrap';


const ListBox = (props) => {
    const animateListItem = useRef();
    const animateTestItem = useRef();
    const [showModal, setShowModal] = useState(false);
    const [tierDetails, setTierDetails] = useState({ title: '', certificate_date: new Date(), documents: [] });


    const title = useRef();
    const itemName = useRef();
    const itemDescription = useRef();
    const itemModel = useRef();
    const itemSerial = useRef();
    const itemDocuments = useRef();

    const customStyles = {

    }

    const handleConfigurationModal = (id) => {

        getTierDetails(props.mode, id);


    }

    const handleDocumentChange = (newValue, newAction) => {
        setTierDetails((prev) => (
            {
                ...prev,
                documents: newValue
            }
        ))
    }

    const handleChecklistChange = (newValue, newAction, id) => {

        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.checklistId = newValue.value;
            }

            return i;
        }))
    }

    const parseDate = (value) => {
        const dateArray = value.split("/");
        return new Date(parseInt(dateArray[2]), parseInt(dateArray[1]) - 1, parseInt(dateArray[0]));
    }

    const getTierDetails = async (mode, id) => {
        console.log('in')
        const response = await API.get(EQUIPMENT_WITH_ID_URL(id));
        if (response.status === 200) {
            console.log(response.data)
            const tierData = response.data;
            const newDocumentData = tierData.documents ? await tierData.documents.map((i) => {

                return props.documents.find(document => document.value === parseInt(i))
            }) : [];




            tierData.documents = newDocumentData;
            // getTestCase(mode, id); use our test case
            setTestCase(tierData.testCase || [])
            setTierDetails(tierData);
            setShowModal(true);
        }
    }

    const handleCreateTest = async (id) => {

        const newTestCase = {
            name: 'Enter Description',
            tierId: tierDetails.id,
            tierMode: props.mode,
            locationId: props.location,
            status: 0,
            maskId: uuidv4(),
            date: '', duration: '', units: '', imca: '', nextDate: '', checklistId: ''

        }
        const response = await API.patch(EQUIPMENT_WITH_ID_URL(tierDetails.id), {
            testCase: [...testCase, newTestCase]
        }
        );



        if (response.status === 204) {

            setTestCase(prev => [...prev, { ...newTestCase, date: '', duration: '', units: '', imca: '', nextDate: '', checklistId: '' }])
            cogoToast.info('Created', { position: 'top-right' })
        }
    }

    const [testCase, setTestCase] = useState([])
    // const getTestCase = async (mode, id) => {
    //     const uriString = {
    //         "where": { tierMode: mode, tierId: id }
    //     }

    //     const url = `${TEST_CASE_URL}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
    //     const response = await fetch(url);
    //     if (response.ok) {
    //         setTestCase(await response.json())
    //     }

    // }

    const handleCreateItem = (e) => {
        e.preventDefault();
        // @ts-ignore
        props.onHandleCreateItem(title.current.value);
        // @ts-ignore
        title.current.value = '';


    }

    const handleTestUpdate = async (id) => {
        // let testCaseData = testCase.find(i => i.maskId === id);

        // if (!testCaseData.nextDate || !dateRegex.test(testCaseData.nextDate)) {
        //     testCaseData.nextDate = calculateNextDate(id);
        // }
        // testCaseData.maskId = testCaseData.maskId ? testCaseData.maskId : uuidv4();
        // testCaseData.locationId = props.location;
        // const response = await API.patch(EQUIPMENT_WITH_ID_URL(tierDetails.id), {
        //     method: 'PATCH',
        //     body: JSON.stringify({
        //         checklistId: testCaseData.checklistId,
        //         date: testCaseData.date,
        //         duration: testCaseData.duration,
        //         imca: testCaseData.imca || '',
        //         locationId: testCaseData.locationId,
        //         maskId: testCaseData.maskId,
        //         name: testCaseData.name,
        //         nextDate: testCaseData.nextDate,
        //         units: testCaseData.units,
        //     }),
        //     headers: {
        //         "Content-type": "application/json; charset=UTF-8"
        //     }
        // });

        // if (response.status === 204) {

        //     singlePopup.fire(
        //         'Updated!',
        //         '',
        //         'success'
        //     );
        // }

    }

    const handleTestDelete = async (id) => {
        //     deletePopup.fire({
        //         title: 'Are you sure?',
        //         text: "You won't be able to revert this!",
        //         icon: 'warning',
        //         showCancelButton: true,
        //         reverseButtons: true,

        //         confirmButtonText: 'Delete'
        //     }).then(async (result) => {
        //         if (result.isConfirmed) {
        //             //   deleteChecklist(id);

        //             const response = await fetch(NEW_TEST_CASE_WITH_ID_URL(id), {
        //                 method: 'DELETE',
        //                 headers: {
        //                     "Content-type": "application/json; charset=UTF-8"
        //                 }
        //             })
        //             if (response.ok) {
        //                 setTestCase(prev => prev.filter(i => i.id !== id));
        //                 singlePopup.fire(
        //                     'Deleted!',
        //                     '',
        //                     'success'
        //                 );
        //             }

        //         }
        //     })
    }
    const handleCardUpdate = async () => {
        //     const response = await fetch(EDIT_TIER_URL(props.mode, tierDetails.id), {
        //         method: 'PATCH',
        //         body: JSON.stringify({
        //             title: itemName.current.value,
        //             description: itemDescription.current.value,
        //             equip_id: itemModel.current.value,
        //             part_id: itemSerial.current.value,
        //             documents: tierDetails.documents.map(i => { return i.value; })
        //         }),
        //         headers: {
        //             "Content-type": "application/json; charset=UTF-8"
        //         }
        //     });

        //     if (response.ok) {
        //         props.changeTitle(tierDetails.id, itemName.current.value)
        //         singlePopup.fire(
        //             'Updated!',
        //             '',
        //             'success'
        //         );
        //     }
        //     setShowModal(false)
    }

    useEffect(() => { animateListItem.current && autoAnimate(animateListItem.current) }, [animateListItem])
    useEffect(() => { animateTestItem.current && autoAnimate(animateTestItem.current) }, [animateTestItem])

    const handleLastDate = (date, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.date = moment(date).format('DD/MM/YYYY');
            }

            return i;
        }))
    }

    const handleNextDate = (date, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.nextDate = moment(date).format('DD/MM/YYYY');
            }

            return i;
        }))
    }

    const handleImcaRef = (event, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.imca = event.target.value;
            }

            return i;
        }))
    }

    const handleTest = (event, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.name = event.target.value;
            }

            return i;
        }))
    }


    const handleDuration = (event, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.duration = event.target.value;
            }

            return i;
        }))


    }


    const handleUnits = (event, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.units = event.target.value;
            }

            return i;
        }))

    }

    const calculateNextDate = (id) => {
        const testCaseData = testCase.find(i => i.id === id);
        const lastDate = parseDate(testCaseData.date);
        let nextGeneratedDate = '';
        switch (testCaseData.units) {
            case 'Hrs':
                nextGeneratedDate = moment(lastDate.setTime(lastDate.getTime() + parseInt(testCaseData.duration) * 60 * 60 * 1000)).format('DD/MM/YYYY');

                break;
            case 'Days':
                nextGeneratedDate = moment(lastDate.setTime(lastDate.getTime() + parseInt(testCaseData.duration) * 24 * 60 * 60 * 1000)).format('DD/MM/YYYY');
                break;

            case 'Months':
                nextGeneratedDate = moment(lastDate.setMonth(lastDate.getMonth() + parseInt(testCaseData.duration))).format('DD/MM/YYYY');
                break;
            default: break;
        }
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.nextDate = nextGeneratedDate
            }

            return i;
        }))

        return nextGeneratedDate;
    }

    const handleDeleteItem = (id) => {
        //     props.handleDeleteItem(props.mode, id)
    }

    const handlePrint = async (id) => {
        //     const mode = props.mode;

        //     try {
        //         const tierResponse = await fetch(EDIT_TIER_URL(mode, id));
        //         const uriString = { "where": { tierMode: mode, tierId: id } };
        //         const url = `${TEST_CASE_URL}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        //         const testCaseResponse = await fetch(url);

        //         if (!tierResponse.ok || !testCaseResponse.ok) {
        //             // Handle the error properly if one of the responses is not ok.
        //             throw new Error('Network response was not ok.');
        //         }

        //         const tierData = await tierResponse.json();
        //         const testCaseData = await testCaseResponse.json();

        //         var equipmentTableBody = [

        //             [{ text: 'Equipment Details', colSpan: 2, alignment: 'center' }, {}],

        //             [{ text: 'Title' }, { text: tierData.title }],
        //             [{ text: tierData.description, colSpan: 2 }, {}],
        //             [{ text: 'Equipment ID' }, { text: tierData.equip_id }],
        //             [{ text: 'Part ID' }, { text: tierData.part_id }]
        //             // ... add more equipment details as needed
        //         ];

        //         var testCasesTableBody = [
        //             [{ text: 'Test Case Details', colSpan: 5, alignment: 'center' }, {}, {}, {}, {}],
        //             [ { text: 'Date' }, { text: 'Duration' }, { text: 'Name' }, { text: 'IMCA' }, { text: 'Next Date' }]
        //             // ... Use the first test case as a header
        //         ];

        //         testCaseData.forEach(function (testCase) {

        //             testCasesTableBody.push([

        //                 { text: testCase.date },
        //                 { text: testCase.duration + ' ' + testCase.units },
        //                 { text: testCase.name },
        //                 { text: testCase.imca },
        //                 { text: testCase.nextDate }
        //             ]);
        //         });

        //         var docDefinition = {
        //             pageOrientation: 'landscape',
        //             pageSize: 'A4',
        //             pageMargins: [20, 10, 20, 20],
        //             content: [
        //                 // Add your logo image here if you have it, for example:
        //                 { image: logoBase64, width: 150, alignment: 'center', margin: [0, 20] },
        //                 { text: 'Certificate of Calibration & Function Testing of Electrically Powered Analysers', alignment: 'center', margin: [0, 20] },
        //                 { text: '', margin: [0, 2] },
        //                 {
        //                     table: {
        //                         widths: ['*', '*'],
        //                         body: equipmentTableBody
        //                     },

        //                 },
        //                 { text: '', margin: [0, 2] },
        //                 {
        //                     table: {
        //                         widths: ['*', '*', '*', '*', '*'],
        //                         body: testCasesTableBody
        //                     },

        //                 },
        //                 { text: '', margin: [0, 2] },


        //             ]
        //         };

        //         pdfMake.createPdf(docDefinition).download('certification-document.pdf');
        //     } catch (error) {
        //         console.error('There was an error processing your request:', error);
        //         // Handle the error properly - for example, showing an alert to the user
        //     }
        // };


        // const handleClone = (id) => {
        //     props.handleClone(props.mode, id)
        // }

        // const handleActivity = (id) => {
        //     props.handleActivity(props.mode, id)
    }

    return (<>
        <div className="card">
            <div className="card-body p-0">
                <div className="card-title pencil-icon p-3 pb-0 mb-0">
                    <Editable initialValue={props.title} onSubmit={(value) => props.onEditTitle(value, props.mode)} className="d-flex" mode="inline" />
                </div>

                {
                    (<>
                        <Form.Group className="form-group p-3 mb-0">
                            <div className="input-group">
                                <Form.Control type="text" ref={title} className="form-control mb-0" placeholder="Enter new item to list" aria-label="item" aria-describedby="basic-addon2" />
                                <div className="input-group-append">
                                    <button className="btn btn-primary btn-icon mb-0 h-100" type="button" onClick={(e) => { handleCreateItem(e) }}><i className="mdi mdi-plus-circle"></i></button>
                                </div>
                            </div>
                        </Form.Group>
                        <div className="h-250" ref={animateListItem}>
                            {
                                props.lists.map((i) => {
                                    return <ListBoxItem key={i.id} selectedId={props.selectedItem.id} data={i} onHandleSelect={(id) => props.handleSelect(id)} onHandleConfigure={handleConfigurationModal} />
                                })
                            }


                        </div>
                    </>)
                }




            </div>

        </div>
        <Modal
            size="lg"
            show={showModal}
            onHide={() => setShowModal(false)}
            aria-labelledby="example-modal-sizes-title-lg"
        >
            <Modal.Header closeButton>
                <Modal.Title>{tierDetails.title}</Modal.Title>
            </Modal.Header>

            <Modal.Body>
                <form className="forms-sample">



                    <div className="row">

                        <div className="col-md-4">
                            <label htmlFor="title"> As New Date</label>

                            <input name="new_date" className="form-control mb-2 mr-sm-2 mb-sm-0 date-field"
                                id="new_date" defaultValue="" placeholder="New Date" />
                        </div>
                        <div className="col-md-4">
                            <label htmlFor="title">When Installed Date</label>

                            <input name="installed_date" className="form-control mb-2 mr-sm-2 mb-sm-0 date-field"
                                id="installed_date" defaultValue="" placeholder="Installed Date" />
                        </div>
                    </div>
                    <div className="form-group">
                        <label htmlFor="title">Item Name</label>
                        <input type="text" ref={itemName} defaultValue={tierDetails.title} className="form-control p-input" id="title" placeholder="Item Name" />
                    </div>

                    <div className="form-group">
                        <label htmlFor="desc">Item Description</label>
                        <textarea ref={itemDescription} defaultValue={tierDetails.description} className="form-control p-input" id="desc"
                            placeholder="Item Description"></textarea>
                    </div>



                    <div className="form-group">
                        <label htmlFor="equip">Model Number</label>
                        <input type="text" ref={itemModel} defaultValue={tierDetails.part_id} className="form-control p-input" id="equip" placeholder="Enter Model Number" />
                    </div>

                    <div className="form-group">
                        <label htmlFor="part">Serial / Asset Number</label>
                        <input type="text" ref={itemSerial} defaultValue={tierDetails.equip_id} className="form-control p-input" id="part"
                            placeholder="Enter Serial / Asset Number" />
                    </div>


                    <div className="form-group trepeater">
                        <label htmlFor="t">Listing of Controls / Frequency / Method</label>
                        {testCase.length > 0 && <Accordion ref={animateTestItem} className="accordion-bordered mb-3 border-left shadow-lg">
                            {
                                testCase.map((i, key) => {
                                    return (
                                        <Accordion.Item key={key} eventKey={key}>
                                            <Accordion.Header>
                                                <div className="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        {i.name}
                                                    </div>
                                                    <div className="me-5">
                                                        Next Due Date: {i.nextDate}
                                                    </div>
                                                </div>
                                            </Accordion.Header>
                                            <Accordion.Body>
                                                <div className="row">
                                                    <div className="col-3">
                                                        <label htmlFor="test">Description</label>
                                                        <input type="text" value={i.name} onChange={(e) => handleTest(e, i.id)} className="form-control p-input" id="test" placeholder="Description" />
                                                    </div>

                                                    <div className="col-3">
                                                        <div className="row">
                                                            <div className="col-6 pe-0">
                                                                <label htmlFor="duration">Frequency</label>
                                                                <input type="number" value={i.duration} onChange={(e) => handleDuration(e, i.id)} className="form-control p-input" id="duration" placeholder="Frequency" />
                                                            </div>
                                                            <div className="col-6 ps-0">
                                                                <label htmlFor="units">Units</label>
                                                                <select value={i.units} onChange={(e) => handleUnits(e, i.id)} className="form-control p-input">
                                                                    <option value="">Select Units</option>
                                                                    <option value="Hrs" >Hours</option>
                                                                    <option value="Days" >Days</option>
                                                                    <option value="Months" >Months</option>
                                                                    <option value="Cycle">Cycles</option>
                                                                </select>
                                                            </div>
                                                        </div>

                                                    </div>

                                                    <div className="col-3">
                                                        <label>Checklist</label>
                                                        <Select
                                                            className="card-select"
                                                            classNamePrefix="card-select"
                                                            styles={customStyles}
                                                            defaultValue={props.checklist.find(c => c.value === i.checklistId)}
                                                            onChange={(newValue, newAction) => handleChecklistChange(newValue, newAction, i.id)}
                                                            isMulti={false}
                                                            options={props.checklist}

                                                        />
                                                    </div>
                                                    <div className="col-3">
                                                        <label htmlFor="ref">Tag / Reference</label>
                                                        <input type="text" value={i.imca} onChange={(e) => handleImcaRef(e, i.id)} className="form-control p-input" id="ref" placeholder="Enter Tags and References separated by comma" />
                                                    </div>
                                                    <div className="col-3">
                                                        <label htmlFor="ldate">Last Task Completion Date</label>
                                                        <DatePicker selected={dateRegex.test(i.date) ? parseDate(i.date) : ''} onChange={date => handleLastDate(date, i.id)} dateFormat="dd/MM/yyyy" className="form-control p-input" style={{ 'z-index': 3 }} />
                                                        {/* <input type="date" defaultValue={i.date} className="form-control p-input" id="ldate" placeholder="Last Inspection Date" /> */}
                                                    </div>
                                                    <div className="col-3">
                                                        <label htmlFor="ldate">Next Due Date <i><a onClick={() => calculateNextDate(i.id)} className="link-primary text-small mb-0 cursor-pointer">Calculate</a></i>
                                                        </label>
                                                        <DatePicker selected={dateRegex.test(i.nextDate) ? parseDate(i.nextDate) : ''} onChange={date => handleNextDate(date, i.id)} dateFormat="dd/MM/yyyy" className="form-control p-input" style={{ 'z-index': 3 }} />
                                                    </div>

                                                </div>
                                                <Button variant="danger me-2" onClick={() => handleTestDelete(i.id)}>Delete</Button>
                                                <Button variant="primary" onClick={() => handleTestUpdate(i.id)}>Update Changes</Button>
                                            </Accordion.Body>
                                        </Accordion.Item>
                                    )
                                })
                            }


                        </Accordion>}
                        <br />
                        <input data-repeater-create type="button" onClick={(e) => { e.preventDefault(); handleCreateTest(); }} className="btn btn-primary ms-2 mb-2" value="+" />

                    </div>


                    <div id="ihtmlForm" className="form-group irepeater hidden">
                        <label htmlFor="t">Consumables / Spares Management</label>
                        <div data-repeater-list="igroup_t3" id="irepeater">

                        </div>
                        <input type="button" id="spare-btn" className="btn btn-primary ms-2 mb-2" value="Save" />
                        <input data-repeater-create type="button" className="btn btn-success ms-2 mb-2" value="+" />

                    </div>

                    <div className="form-group">
                        <label>Choose Documents (Click or Type to Choose Multiple Documents)</label>
                        <Select
                            onChange={handleDocumentChange}
                            isMulti={true}
                            options={props.documents}
                            defaultValue={tierDetails.documents}
                        />
                    </div>

                </form>
            </Modal.Body>

            <Modal.Footer>

                <Button variant="light" onClick={() => setShowModal(false)}>Cancel</Button>
                <Button variant="primary" onClick={handleCardUpdate}>Update Changes</Button>
            </Modal.Footer>
        </Modal>

    </>);
}

export default ListBox;
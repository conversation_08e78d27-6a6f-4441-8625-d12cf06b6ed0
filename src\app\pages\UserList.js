import React, { useState, useMemo, useEffect } from 'react'
import User from './User'
import IndiaUser from './IndiaUser'
import ThailandUser from './ThailandUser'
import KoreaUser from './KoreaUser'
import JapanUser from './JapanUser'
import IndonesiaUser from './IndonesiaUser'

import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import UkUser from './UkUser'
import { useSelector } from 'react-redux'
function CustomTabPanel(props) {
    const { children, value, tabValue, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== tabValue}
            id={`incident-tabpanel-${tabValue}`}
            aria-labelledby={`incident-tab-${tabValue}`}
            {...other}
        >
            {value === tabValue && (
                <Box >

                    {children}
                </Box>
            )}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `incident-tab-${index}`,
        'aria-controls': `incident-tabpanel-${index}`,
    };
}



const UserList = () => {

    const [value, setValue] = useState(''); // default tab
    const [selectedTab, setSelectedTab] = useState('');
    const user = useSelector(state => state.login.user);

    const TOP_LEVEL_TABS = {
        INDIA: "India (IN)",
        UK: "UK (UK)",
        SINGAPORE: "Singapore (SG)",

    };
    const tabs = [
        { label: "India", value: "India", component: IndiaUser, role: "India (IN)" },
        { label: "UK", value: "UK", component: UkUser, role: "UK (UK)" },
        { label: "Singapore", value: "Singapore", component: User, role: "Singapore (SG)" },
        { label: "Thailand", value: "Thailand", component: ThailandUser, role: "Thailand (TH)" },
        { label: "Korea", value: "Korea", component: KoreaUser, role: "Korea (KR)" },
        { label: "Japan", value: "Japan", component: JapanUser, role: "Japan (JP)" },
        { label: "Indonesia", value: "Indonesia", component: IndonesiaUser, role: "Indonesia (ID)" },
    ];

    const availableTabs = tabs.filter(tab => user?.validationRoles?.some(role => role.name === tab.role));

    useEffect(() => {
        if (availableTabs.length > 0 && !selectedTab) {
            setSelectedTab(availableTabs[0].value);
        }
    }, [availableTabs, selectedTab]);

    useEffect(() => {

        setValue(TOP_LEVEL_TABS.INDIA)
    }, [])

    const me = useSelector((state) => state.login.user);

    const handleChange = (event, newValue) => {
        setSelectedTab(newValue);
    };

    // Memoize the check for roles
    const roles = useMemo(() => ({
        isIndia: me?.validationRoles?.some(role => role.name === 'India (IN)'),
        isUK: me?.validationRoles?.some(role => role.name === 'UK (UK)'),
        isSingapore: me?.validationRoles?.some(role => role.name === 'Singapore (SG)'),
        isThailand: me?.validationRoles?.some(role => role.name === 'Thailand (TH)'),
        isIndonesia: me?.validationRoles?.some(role => role.name === 'Indonesia (ID)'),
        isPhilippines: me?.validationRoles?.some(role => role.name === 'Philippines (PH)'),
        isJapan: me?.validationRoles?.some(role => role.name === 'Japan (JP)'),
        isKorea: me?.validationRoles?.some(role => role.name === 'Korea (KR)'),
    }), [me]);

    return (
        <Box sx={{ width: '100%' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={selectedTab} onChange={handleChange} aria-label="User categories">
                    {availableTabs.map(tab => (
                        <Tab key={tab.value} label={tab.label} value={tab.value} />
                    ))}
                </Tabs>
            </Box>
            {availableTabs.map(tab => (
                <CustomTabPanel key={tab.value} value={selectedTab} tabValue={tab.value}>
                    <tab.component />
                </CustomTabPanel>
            ))}
        </Box>


    );
}

export default UserList;
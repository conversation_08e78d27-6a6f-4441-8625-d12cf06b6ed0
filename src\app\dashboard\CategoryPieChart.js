import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import { getDisplayDateRange } from "./dateUtils";

const CategoryPieChart = ({ info, dateRange }) => {
    const displayDateRange = getDisplayDateRange(dateRange);

    const [category, setCategory] = useState({
        totalCategories: 3,
        categoryPercentages: {
            Safety: "95.98%",
            Health: "2.13%",
            Environment: "1.89%",
        },
    });

    useEffect(() => {
        if (info) {
            setCategory(info);
        }
    }, [info]);

    // Extract labels and percentages
    const labels = Object.keys(category.categoryPercentages);
    const dataValues = Object.values(category.categoryPercentages).map((value) => parseFloat(value));

    // Highcharts Pie Chart Configuration
    const options = {
        chart: {
            type: "pie",
        },
        title: {
            text: ``,
        },
        tooltip: {
            pointFormat: "<b>{point.name}</b>: {point.percentage:.1f}%",
        },
        accessibility: {
            point: {
                valueSuffix: "%",
            },
        },
        plotOptions: {
            pie: {
                allowPointSelect: true,
                cursor: "pointer",
                dataLabels: {
                    enabled: true,
                    format: "<b>{point.name}</b>: {point.percentage:.1f}%",
                },
                showInLegend: true, // 🔹 Show legend for interactivity
            },
        },
        legend: {
            enabled: true, // 🔹 Enables legend interaction
            labelFormatter: function () {
                return `${this.name} (${this.percentage.toFixed(1)}%)`; // Custom legend format
            },
        },
        series: [
            {
                name: "Percentage",
                colorByPoint: true,
                data: labels.map((label, index) => ({
                    name: label,
                    y: dataValues[index],
                    visible: true, // Default visibility
                })),
            },
        ],
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    menuItems: [
                        "downloadPNG",
                        "downloadJPEG",
                        "downloadPDF",
                        "downloadSVG",
                        "separator",
                        "downloadCSV",
                        "downloadXLS",
                    ],
                },
            },
        },
    };

    return (
        <div style={{ width: "100%", margin: "auto" }}>
            <HighchartsWrapper options={options} />
        </div>
    );
};

export default CategoryPieChart;

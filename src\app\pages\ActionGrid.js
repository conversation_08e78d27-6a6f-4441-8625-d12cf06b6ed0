import React from 'react'
import * as Icon from 'feather-icons-react';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
function ActionCard({ count, title, url, color, desc }) {
    const history = useHistory()
    const seturl = () => {
        history.push(url)
    }
    return (
        <div className='col-3 mb-4 mt-1' onClick={() => seturl()}>
            <div className='cardShadow' style={{ borderBottomWidth: '8px', borderBottomStyle: 'solid', borderBottomColor: color }}>
                <h1 className='cardCount mb-4'>{count}</h1>
                <h4 className='cardTitle mb-3'>{title}</h4>
                <p className='cardDesc ' style={{minHeight:40}}>{desc}  </p>

                <p className='cardLink'>Go to {title} <Icon.ArrowUpRight currentColor={'#363F72'} /></p>

            </div>
        </div>
    )
}

export default ActionCard
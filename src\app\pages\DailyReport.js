import React, { Component, useState, useEffect } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { ALL_REPORT_DATA_URL, OBSERVATION_REPORT_URL, OBSERVATION_REPORT_WITH_ID, STATIC_URL } from '../constants';

import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import ObservationModal from './ObservationModal';
import { dailyReportColumns, tableOptions } from './TableColumns';
import CardOverlay from './CardOverlay';
import FilterLocation from './FilterLocation';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import AllFilterLocation from './AllLocationFilter';
import { Button, Modal } from 'react-bootstrap';
import ReportModal from '../shared/ReportModal';

import moment from 'moment';

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const DailyReport = () => {

    const [formData, setFormData] = useState(
        {
            "date": moment().format('DD/MM/YYYY'),
            "numberOfEmployees": 0,
            "dailyHoursOfEmployee": 0,
            "numberofContractors": 0,
            "dailyHoursOfContractors": 0,
            "yearAndMonth": "",
            "noOfSafety": 0,
            "noOfToolbox": 0,
            "noOfEhsTraining": 0,
            "noOfInspection": 0,
            "noOfManagmentSiteWalk": 0,
            "noOfAuthority": 0,
            "noOfSafeObservation": 0,
            "noOfRiskObservation": 0,
            "type": "",

        }
    )

    const history = useHistory();
    const thead = [
        'id',
        'Type',
        'Category',
        'Description',
        'Rectified Status',
        'Reported By',
        'Remarks',
        'Action Taken',


    ];
    const defaultMaterialTheme = createTheme();

    const [data, setData] = useState([]);
    const [filterData, setFilterData] = useState([]);

    useEffect(() => {
        getObservationData();
    }, [])

    const getObservationData = async () => {

        const params = {
            "include": [{ "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }]

        };
        const response = await API.get(`${ALL_REPORT_DATA_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
        if (response.status === 200) {
            setData(response.data.filter(i => i.type === 'daily'))
            setFilterData(response.data.filter(i => i.type === 'daily'))

        }
    }



    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };




    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        const filteredData = data.filter(item => {
            return (
                (locationOneId === '' || item.locationOneId === locationOneId) &&
                (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
                (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
                (locationFourId === '' || item.locationFourId === locationFourId)
            );
        });

        setFilterData(filteredData);
    };

    const [showModal, setShowModal] = useState(false)

    const handleAddData = async () => {
        // Logic to add new data
        const response = await API.post(ALL_REPORT_DATA_URL, {

            "date": formData.date,
            "numberOfEmployees": parseInt(formData.numberOfEmployees),
            "dailyHoursOfEmployee": parseInt(formData.dailyHoursOfEmployee),
            "numberofContractors": parseInt(formData.numberofContractors),
            "dailyHoursOfContractors": parseInt(formData.dailyHoursOfContractors),
            "yearAndMonth": formData.yearAndMonth,
            "noOfSafety": parseInt(formData.noOfSafety),
            "noOfToolbox": parseInt(formData.noOfToolbox),
            "noOfEhsTraining": parseInt(formData.noOfEhsTraining),
            "noOfInspection": parseInt(formData.noOfInspection),
            "noOfManagmentSiteWalk": parseInt(formData.noOfManagmentSiteWalk),
            "noOfAuthority": parseInt(formData.noOfAuthority),
            "noOfSafeObservation": parseInt(formData.noOfSafeObservation),
            "noOfRiskObservation": parseInt(formData.noOfRiskObservation),
            "type": "daily",

            "locationOneId": formData.locationOneId,
            "locationTwoId": formData.locationTwoId,
            "locationThreeId": formData.locationThreeId,
            "locationFourId": formData.locationFourId

        })

        if (response.status === 200) {
            setShowModal(false)
            setFormData({
                "date": moment().format('DD/MM/YYYY'),
                "numberOfEmployees": 0,
                "dailyHoursOfEmployee": 0,
                "numberofContractors": 0,
                "dailyHoursOfContractors": 0,
                "yearAndMonth": "",
                "noOfSafety": 0,
                "noOfToolbox": 0,
                "noOfEhsTraining": 0,
                "noOfInspection": 0,
                "noOfManagmentSiteWalk": 0,
                "noOfAuthority": 0,
                "noOfSafeObservation": 0,
                "noOfRiskObservation": 0,
                "type": "",

            })
            getObservationData()
            cogoToast.success('Submitted!')
        }
    };



    return (
        <>
            <CardOverlay>
                <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} />
                <ThemeProvider theme={defaultMaterialTheme}>
                    <Button variant="primary" onClick={() => setShowModal(true)}>
                        Add Data
                    </Button>
                    <MaterialTable
                        columns={dailyReportColumns}
                        data={filterData}
                        title="Daily Report"
                        style={tableStyle}

                        options={{ pageSize: 20, ...tableOptions }}

                    />
                </ThemeProvider>

                <Modal show={showModal} onHide={() => setShowModal(false)}>
                    <Modal.Header closeButton>
                        <Modal.Title>Reports</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <ReportModal type="daily" formData={formData} setFormData={setFormData} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Close
                        </Button>
                        <Button variant="primary" onClick={handleAddData}>
                            Submit
                        </Button>
                    </Modal.Footer>
                </Modal>

            </CardOverlay>

        </>
    )
}

export default DailyReport;

import React, { useState } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import moment from "moment";

const SafetyMetricsChart = ({ data, dateRange }) => {
    const [selectedMetrics, setSelectedMetrics] = useState({
        safetyInductions: true,
        toolboxMeetings: true,
        ehsTrainings: true,
        ehsInspections: true,
        ngoVisits: true,
        managementWalks: true,
    });

    const monthMap = {
        jan: 0, feb: 1, mar: 2, apr: 3, may: 4, jun: 5,
        jul: 6, aug: 7, sep: 8, oct: 9, nov: 10, dec: 11,
    };

    // Ensure start and end date are first day of the month
    const [startDate, endDate] = dateRange.map((date) => {
        const parsed = new Date(date);
        return new Date(parsed.getFullYear(), parsed.getMonth(), 1);
    });

    // Step 1: Generate all months in the range
    const monthsInRange = [];
    const current = new Date(startDate);
    while (current <= endDate) {
        const key = `${current.getFullYear()}-${current.getMonth()}`;
        monthsInRange.push({
            key,
            year: current.getFullYear(),
            monthIndex: current.getMonth(),
            dateKey: new Date(current),
        });
        current.setMonth(current.getMonth() + 1);
    }

    // Step 2: Aggregate actual data
    const aggregatedData = {};

    data.forEach((d) => {
        const monthIndex = monthMap[d.month?.toLowerCase()];
        if (monthIndex === undefined || d.year === undefined) return;

        const key = `${d.year}-${monthIndex}`;
        if (!aggregatedData[key]) {
            aggregatedData[key] = {
                year: d.year,
                monthIndex,
                dateKey: new Date(d.year, monthIndex, 1),
                noOfSafetyInductionsConducted: 0,
                noOfToolboxMeetingsSafetyBriefingsSafeStarts: 0,
                noOfEHSTrainings: 0,
                noOfEHSInspectionsAudits: 0,
                authorityNGOUnionVisits: 0,
                noOfManagementSiteWalkInspection: 0,
            };
        }

        aggregatedData[key].noOfSafetyInductionsConducted += d.noOfSafetyInductionsConducted || 0;
        aggregatedData[key].noOfToolboxMeetingsSafetyBriefingsSafeStarts += d.noOfToolboxMeetingsSafetyBriefingsSafeStarts || 0;
        aggregatedData[key].noOfEHSTrainings += d.noOfEHSTrainings || 0;
        aggregatedData[key].noOfEHSInspectionsAudits += d.noOfEHSInspectionsAudits || 0;
        aggregatedData[key].authorityNGOUnionVisits += d.authorityNGOUnionVisits || 0;
        aggregatedData[key].noOfManagementSiteWalkInspection += d.noOfManagementSiteWalkInspection || 0;
    });

    // Step 3: Merge range with actual data to fill in 0s
    const processedData = monthsInRange.map(({ key, year, monthIndex, dateKey }) => {
        return aggregatedData[key] || {
            year,
            monthIndex,
            dateKey,
            noOfSafetyInductionsConducted: 0,
            noOfToolboxMeetingsSafetyBriefingsSafeStarts: 0,
            noOfEHSTrainings: 0,
            noOfEHSInspectionsAudits: 0,
            authorityNGOUnionVisits: 0,
            noOfManagementSiteWalkInspection: 0,
        };
    });

    // Labels for x-axis
    const labels = processedData.map((d) =>
        moment(d.dateKey).format("MMM YYYY")
    );

    // Prepare series data based on selected metrics
    const seriesData = [
        selectedMetrics.safetyInductions && {
            name: "Safety Inductions",
            data: processedData.map((d) => d.noOfSafetyInductionsConducted),
        },
        selectedMetrics.toolboxMeetings && {
            name: "Toolbox/Safety Briefings",
            data: processedData.map((d) => d.noOfToolboxMeetingsSafetyBriefingsSafeStarts),
        },
        selectedMetrics.ehsTrainings && {
            name: "EHS Trainings",
            data: processedData.map((d) => d.noOfEHSTrainings),
        },
        selectedMetrics.ehsInspections && {
            name: "EHS Inspections/Audits",
            data: processedData.map((d) => d.noOfEHSInspectionsAudits),
        },
        selectedMetrics.ngoVisits && {
            name: "NGO/Union Visits",
            data: processedData.map((d) => d.authorityNGOUnionVisits),
        },
        selectedMetrics.managementWalks && {
            name: "Management Walks",
            data: processedData.map((d) => d.noOfManagementSiteWalkInspection),
        },
    ].filter(Boolean); // Remove unselected metrics

    // Highcharts Configuration
    const options = {
        chart: {
            type: "column",
            zoomType: "xy",
        },
        title: {
            text: "",
        },
        xAxis: {
            categories: labels,
            title: {
                text: "Month-Year",
            },
            crosshair: true,
        },
        yAxis: {
            title: {
                text: "Number of Events",
            },
            min: 0,
        },
        tooltip: {
            shared: true,
            valueSuffix: " events",
        },
        plotOptions: {
            column: {
                stacking: "normal",
                dataLabels: {
                    enabled: true,
                },
            },
        },
        legend: {
            enabled: true,
            layout: "horizontal",
            align: "center",
            verticalAlign: "bottom",
        },
        series: seriesData,
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    menuItems: [
                        "downloadPNG",
                        "downloadJPEG",
                        "downloadPDF",
                        "downloadSVG",
                        "separator",
                        "downloadCSV",
                        "downloadXLS",
                    ],
                },
            },
        },
    };

    return (
        <div>
            {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
        </div>
    );
};

export default SafetyMetricsChart;

import React, { useState, useEffect } from "react";
import SingleFilterLocation from "./SingleFilterLocation";
import { CHECKLIST_URL, AUDIT_URL } from '../constants';
import cogoToast from 'cogo-toast';
import API from '../services/API';
import AuditForm from "./AuditForm";
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const AuditScheduling = ({ locationOneId, locationTwoId, locationThreeId, locationFourId }) => {
    console.log(locationOneId, locationTwoId, locationThreeId, locationFourId)

    const [location, setLocation] = useState(
        {
            locationOneId: '',
            locationTwoId: '',
            locationThreeId: '',
            locationFourId: ''
        }
    )
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();
    const years = [currentYear, currentYear + 1];
    const [selectedYear, setSelectedYear] = useState(currentYear);

    useEffect(() => {
        setLocation({ locationOneId: locationOneId, locationTwoId: locationTwoId, locationThreeId: locationThreeId, locationFourId: locationFourId })

    }, [locationOneId, locationTwoId, locationThreeId, locationFourId])


    useEffect(() => {
        getChecklist()
    }, [selectedYear])


    const [checklist, setChecklist] = useState([]);


    const getChecklist = async () => {
        const response = await API.get(CHECKLIST_URL);
        if (response.status === 200) {
            return response.data.filter(i => i.application === 'Audit').map(i => ({ ...i, monthlyStatus: new Array(12).fill('') }));
        }
        return [];
    }

    useEffect(() => {
        // This will fetch and update the checklist data initially and whenever the location or selectedYear changes
        const updateData = async () => {
            const fetchedChecklist = await getChecklist();
            setChecklist(fetchedChecklist);
            if (location.locationOneId && location.locationTwoId && location.locationThreeId && location.locationFourId && selectedYear) {
                getAudit();
            }
        };

        updateData();
    }, [location.locationOneId, location.locationTwoId, location.locationThreeId, location.locationFourId, selectedYear]);




    const [inspection, setInspection] = useState([]);



    const getAudit = async () => {
        const response = await API.get(AUDIT_URL);
        if (response.status === 200) {
            console.log(response.data, location.locationFourId, selectedYear)
            setInspection(response.data.filter(i => i.locationOneId === location.locationOneId && i.locationTwoId === location.locationTwoId && i.locationThreeId === location.locationThreeId && i.locationFourId === location.locationFourId && parseInt(i.year) === parseInt(selectedYear)));

        }
    }

    useEffect(() => {
        // This will update the checklist with inspection data
        if (inspection.length > 0) {
            updateChecklistWithInspection(inspection);
        }
    }, [inspection]);

    const [value, setValue] = useState(0);
    const [topLevelValue, setTopLevelValue] = useState(0);

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };


    const updateChecklistWithInspection = (inspectionData) => {
        setChecklist((currentChecklist) => {
            const newChecklist = currentChecklist.map((checklistItem) => {
                const updatedStatus = [...checklistItem.monthlyStatus];
                // Find the inspection data for this checklist item
                inspectionData.forEach((inspection) => {
                    if (inspection.checklistId === checklistItem.id) {
                        // Convert month string to index (Jan = 0, Feb = 1, etc.)
                        const monthIndex = months.indexOf(inspection.month);
                        if (monthIndex !== -1) {
                            // Update the status for the corresponding month
                            updatedStatus[monthIndex] = inspection.status;
                        }
                    }
                });
                return { ...checklistItem, monthlyStatus: updatedStatus };
            });

            return newChecklist;
        });
    }

    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
    //     setLocation({ locationOneId: locationOneId, locationTwoId: locationTwoId, locationThreeId: locationThreeId, locationFourId: locationFourId })
    // };

    const [popupData, setPopupData] = useState({ month: '', checklist: null, location: null });
    const [showModal, setShowModal] = useState(false);
    const handleCellClick = (monthIndex, checklistId) => {

        const selectedChecklistItem = checklist.find(i => i.id === checklistId);

        setPopupData({ month: months[monthIndex], checklist: selectedChecklistItem, location: location });
        setShowModal(true);
    };

    const handleSubmit = async (formData) => {
        formData.year = `${selectedYear}`;
        formData.locationOneId = location.locationOneId;
        formData.locationTwoId = location.locationTwoId;
        formData.locationThreeId = location.locationThreeId;
        formData.locationFourId = location.locationFourId;
        formData.status = 'Initiated'

        const response = await API.post(AUDIT_URL, formData)
        if (response.status === 200) {
            cogoToast.success('Submitted!')
            getChecklist();
            getAudit();
            setPopupData({ month: '', checklist: null, location: null })
            setShowModal(false)
        }
    }
    const renderCell = (audit, monthIndex, monthName) => {
        const isPast = selectedYear < currentYear || (selectedYear === currentYear && monthIndex < currentMonth);
        const status = audit.monthlyStatus[monthIndex];

        return (
            <div
                className={`cursor-pointer ${isPast ? 'disabled-cell' : ''}`}
                onClick={() => !isPast && handleCellClick(monthIndex, audit.id)}
            >
                {status ?
                    <span className="initiated-cell">{status}</span>
                    :
                    (!isPast && <span>Initiate Audit</span>)
                }

            </div>
        );
    };
    const headerTemplate = () => {
        return (
            <div className="row mb-3">
                <div className="col-md-4">
                    <label htmlFor="yearSelector" className="form-label">Select Year:</label>
                    <select
                        id="yearSelector"
                        className="form-select"
                        value={selectedYear}
                        onChange={(e) => setSelectedYear(e.target.value)}
                    >
                        {years.map(year => (
                            <option key={year} value={year}>
                                {year}
                            </option>
                        ))}
                    </select>
                </div>
            </div>
        )
    }

    const rows = months.map((month, monthIndex) => {
        const row = { month };
        checklist.forEach((audit) => {
            row[audit.name] = renderCell(audit, monthIndex, month);
        });
        return row;
    });
    return (
        <>
            <div className=" ">
                {/*  */}
                {/* <SingleFilterLocation handleFilter={handleFilter} /> */}


                {/* {
                    (location.locationOneId && location.locationTwoId && location.locationThreeId && location.locationFourId) &&
                    <div className='table-responsive'>
                        <table className="table table-bordered table-striped table-responsive">
                            <thead>
                                <tr>
                                    <th><strong>Type of Audit</strong></th>
                                    {months.map(month => (
                                        <th key={month}><strong>{month}</strong></th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                               
                                {checklist.map(c => (
                                    <tr key={c.id}>
                                        <td>{c.name}</td>
                                      
                                        {c.monthlyStatus.map((status, index) => {

                                            const isPast = selectedYear < currentYear || (selectedYear === currentYear && index < currentMonth);

                                            return (
                                                <td key={index} className={`cursor-pointer ${isPast ? 'disabled-cell' : ''}`} onClick={() => !isPast && handleCellClick(index, c.id)}>
                                                    {status}
                                                    {!status && !isPast && <i className='mdi mdi-plus-circle'></i>}
                                                </td>)
                                        })}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                } */}
                {(location.locationOneId && location.locationTwoId && location.locationThreeId && location.locationFourId) &&
                    <DataTable value={rows} header={headerTemplate}>
                        <Column field="month" header="Months" />
                        {checklist.map((audit) => (
                            <Column key={audit.id} field={audit.name} header={audit.name} />
                        ))}
                    </DataTable>
                }
            </div>
            {(showModal && popupData.month && popupData.checklist) && <AuditForm
                show={showModal}
                onHide={() => setShowModal(false)}
                month={popupData.month}
                year={selectedYear}
                checklist={popupData.checklist}
                location={popupData.location}
                onSubmit={(formData) => {
                    // Handle form submission to the AUDIT_URL
                    handleSubmit(formData)
                }}
            />}
        </>
    )
}


export default AuditScheduling;
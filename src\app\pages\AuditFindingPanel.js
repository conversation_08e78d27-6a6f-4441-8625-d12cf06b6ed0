import React, { useState, useEffect } from "react";
import API from "../services/API";
import { AUDIT_FINDINGS_ASSIGN_ACTION_URL, AUDIT_GMS3_URL, AUDIT_WITH_ID_URL, INSPECTION_ACTION_PLAN_REVIEWER, STATIC_URL } from "../constants";
import { Tab, Tabs, Table } from 'react-bootstrap';
import GalleryPage from "../apps/Gallery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from "cogo-toast";

const AuditFindingPanel = ({ auditId }) => {
    useEffect(() => {
        if (auditId) {
            getAuditAllGmsThree();

            getAuditInfo()
        }
    }, [auditId]);
    const [audit, setAudit] = useState(null);

    useEffect(() => {
        if (audit) {
            getActionImplementors();
        }
    }, [audit])

    const getAuditInfo = async () => {

        const response = await API.get(AUDIT_WITH_ID_URL(auditId))
        if (response.status === 200) {
            setAudit(response.data)
        }
    }
    const [users, setUsers] = useState([])
    const [nc, setNc] = useState([]);
    const [ofi, setOfi] = useState([]);
    const [selectedFindingListData, setSelectedFindingListData] = useState([]);
    const [key, setKey] = useState('Non-Conformances'); // State to manage active tab

    const getActionImplementors = async () => {
        const response = await API.post(INSPECTION_ACTION_PLAN_REVIEWER, { locationOneId: audit.locationOneId, locationTwoId: audit.locationOneId, locationThreeId: audit.locationOneId, locationFourId: audit.locationOneId });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }
    const getAuditAllGmsThree = async () => {
        const params = { "include": [{ "relation": "auditFindings" }] };
        const url = `${AUDIT_GMS3_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`;
        const response = await API.get(url);
        if (response.status === 200 && response.data) {
            const allGmsThree = response.data;

            const ncFindings = [];
            const ofiFindings = [];
            console.log(allGmsThree, 'gmsThree')
            allGmsThree.forEach(gmsItem => {
                gmsItem.auditFindings?.forEach(finding => {
                    if (finding.category === 'Non-Conformances' && finding.auditId === auditId) {
                        ncFindings.push(finding);
                    } else if (finding.category === 'Opportunity For Improvement' && finding.auditId === auditId) {
                        ofiFindings.push(finding);
                    }
                });
            });
            console.log(ncFindings, ofiFindings)
            setNc(ncFindings);
            setOfi(ofiFindings);
        }
    };

    useEffect(() => {
        // Update selectedFindingListData based on the active tab
        setSelectedFindingListData(key === 'Non-Conformances' ? nc : ofi);
    }, [key, nc, ofi]);
    const generateTableHeaders = (category) => {
        switch (category) {
            case 'Good Practices':
                return (
                    <tr>
                        <th>#</th>
                        <th>Findings</th>
                        <th>GMS Section</th>
                        <th>Recommendations</th>
                        <th>Uploads</th>
                    </tr>
                );
            case 'Non-Conformances':
                return (
                    <tr>
                        <th>#</th>
                        <th>Findings</th>
                        <th>GMS Section</th>
                        <th>Classification</th>
                        <th>Potential Hazard</th>
                        <th>Standards & References</th>
                        <th>Time Frame</th>
                        <th>Uploads</th>
                        <th></th>
                    </tr>
                );
            case 'Opportunity For Improvement':
                return (
                    <tr>
                        <th>#</th>
                        <th>Findings</th>
                        <th>GMS Section</th>
                        <th>Recommendations</th>
                        <th>Standards & References</th>
                        <th>Uploads</th>
                        <th></th>
                    </tr>
                );
            default:
                return null;
        }
    };

    const [assignActionModal, setAssignActionModal] = useState(false)
    const [selectedFinding, setSelectedFinding] = useState('')
    const [selectedUser, setSelecedUser] = useState('')
    const [actionToBeTaken, setActionToBeTaken] = useState('Please address the root cause of the above non-conformance, identify and implement corrective actions within the stipulated time period and provide documentary evidence of the same from within the portal.')
    const [dueDate, setDueDate] = useState(new Date())

    const assignAction = async (id) => {
        setSelecedUser('')
        setAssignActionModal(true)
        setSelectedFinding(id)
    }

    const handleAssignAction = async () => {
        const response = await API.patch(AUDIT_FINDINGS_ASSIGN_ACTION_URL(selectedFinding), { dueDate: dueDate, actionToBeTaken: actionToBeTaken, userId: selectedUser })
        if (response.status === 204) {
            cogoToast.success('Action Assigned!')
            setSelecedUser('')
            setAssignActionModal(false)
            setSelectedFinding('')
            getAuditAllGmsThree()
        }
    }
    const generateTableRow = (item) => {
        const modifiedUploads = item.uploads ? item.uploads.map(i => {
            return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
        }) : []
        switch (item.category) {
            case 'Good Practices':
                return (
                    <tr key={item.id}>
                        <td>{item.maskId}</td>
                        <td>{item.findings}</td>
                        <td>{item.inspectionCategories.name}</td>
                        <td>{item.recommendations}</td>
                        <td><GalleryPage photos={modifiedUploads} /></td>
                    </tr>
                );
            case 'Non-Conformances':
                return (
                    <tr key={item.id}>
                        <td>{item.maskId}</td>
                        <td>{item.findings}</td>
                        <td>{item.inspectionCategories.name}</td>
                        <td>{item.classification}</td>
                        <td>{item.potentialHazard}</td>
                        <td>{item.standardsAndReferences}</td>
                        <td>{item.timeFrame}</td>
                        <td><GalleryPage photos={modifiedUploads} /></td>
                        <td>
                            {item.actionAssigned ? (<i className=""></i>) : <i onClick={() => assignAction(item.id)} className="mdi mdi-account-multiple-plus icon-md cursor-pointer"></i>}
                        </td>
                    </tr>
                );
            case 'Opportunity For Improvement':
                return (
                    <tr key={item.id}>
                        <td>{item.maskId}</td>
                        <td>{item.findings}</td>
                        <td>{item.inspectionCategories.name}</td>
                        <td>{item.recommendations}</td>
                        <td>{item.standardsAndReferences}</td>
                        <td><GalleryPage photos={modifiedUploads} /></td>
                        <td>
                            {item.actionAssigned ? (<i className=""></i>) : <i onClick={() => assignAction(item.id)} className="mdi mdi-account-multiple-plus icon-md cursor-pointer"></i>}
                        </td>
                    </tr>
                );
            default:
                return null;
        }
    };

    return (
        <>
            <Tabs
                id="controlled-tab-example"
                activeKey={key}
                onSelect={(k) => setKey(k)}
                className="mb-3"
            >
                <Tab eventKey="Non-Conformances" title="Non-Conformances">
                    <div className="table-responsive">
                        <Table className="table table-bordered table-striped">
                            <thead>
                                {selectedFindingListData.length > 0 && generateTableHeaders('Non-Conformances')}
                            </thead>
                            <tbody>
                                {selectedFindingListData.map((item) => generateTableRow(item))}
                            </tbody>
                        </Table>
                    </div>
                </Tab>
                {/* <Tab eventKey="Opportunity for Improvement" title="Opportunity for Improvement">
                    <div className="table-responsive">
                        <Table className="table table-bordered table-striped">
                            <thead>
                                {selectedFindingListData.length > 0 && generateTableHeaders('Opportunity for Improvement')}
                            </thead>
                            <tbody>
                                {selectedFindingListData.map((item) => generateTableRow(item))}
                            </tbody>
                        </Table>
                    </div>
                </Tab> */}
            </Tabs>

            {assignActionModal &&

                <Modal
                    className="nested-modal"
                    show={assignActionModal}
                    size="md"
                    onHide={() => { setSelectedFinding(''); setAssignActionModal(false) }}
                    aria-labelledby="example-modal-sizes-title-md"
                    backdropClassName="nested-modal"
                    centered
                >
                    <Modal.Header>

                        Assign User

                    </Modal.Header>

                    <Modal.Body>
                        <div className="form-group">
                            <label>Action To Be Taken</label>
                            <p>Please address the root cause of the above non-conformance, identify and implement corrective actions within the stipulated time period and provide documentary evidence of the same from within the portal.</p>
                            {/* <input type="text" value={actionToBeTaken} onChange={(e) => setActionToBeTaken(e.target.value)} className="form-control" placeholder="Action To Be Taken" /> */}
                        </div>
                        <div className="form-group">
                            <label>Due Date</label>
                            <input type="date" value={dueDate} onChange={(e) => setDueDate(e.target.value)} className="form-control" />
                        </div>
                        <div className="form-group">
                            <select className="form-select" onChange={(e) => setSelecedUser(e.target.value)}>
                                <option value={""}>Assign User</option>
                                {
                                    users.map(i => <option value={i.id} key={i.id}>{i.firstName}</option>)
                                }
                            </select>
                        </div>

                    </Modal.Body>
                    <Modal.Footer>
                        <button onClick={handleAssignAction} className="btn btn-primary me-2">Submit</button>
                        <button onClick={() => { setSelectedFinding(''); setAssignActionModal(false) }} className="btn btn-light">Close</button>
                    </Modal.Footer>
                </Modal>


            }
        </>
    );
}

export default AuditFindingPanel;
import React, { useState, useEffect } from "react";
import API from "../services/API";
import { LOCATION1_URL, LOCATION_TIER1_URL, TIER1_TIER2_URL, TIER2_TIER3_URL, DYNAMIC_TITLES_URL, LOCATION2_URL, LOCATION3_URL, LOCATION4_URL, API_URL } from "../constants";

import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { Button } from 'primereact/button'

const AllFilterLocation = (props) => {
    const BASE_URL = `${API_URL}/`; // Example base URL

    const [locationOne, setLocationOne] = useState([]);
    const [locationTwo, setLocationTwo] = useState([]);
    const [locationThree, setLocationThree] = useState([]);
    const [locationFour, setLocationFour] = useState([]);

    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);


    const [selectedLocationOne, setSelectedLocationOne] = useState({ name: 'All', title: 'All', id: '' });
    const [selectedLocationTwo, setSelectedLocationTwo] = useState({ name: 'All', title: 'All', id: '' });
    const [selectedLocationThree, setSelectedLocationThree] = useState({ name: 'All', title: 'All', id: '' });
    const [selectedLocationFour, setSelectedLocationFour] = useState({ name: 'All', title: 'All', id: '' });


    useEffect(() => {
        const fetchLocationOne = async () => {
            const response = await API.get(`${BASE_URL}location-ones`);
            if (response.status === 200) {

                const newObj = { name: 'All', title: 'All', id: '' };

                setLocationOne([newObj, ...response.data]);
            }
        };
        fetchLocationOne();
    }, []);

    // Fetch Location Two based on Location One selection
    useEffect(() => {
        if (selectedLocationOne.id === '') return;
        const fetchLocationTwo = async () => {
            const response = await API.get(`${BASE_URL}location-ones/${selectedLocationOne.id}/location-twos`);
            if (response.status === 200) {

                const newObj = { name: 'All', title: 'All', id: '' };

                setLocationTwo([newObj, ...response.data]);
            }
        };
        fetchLocationTwo();
    }, [selectedLocationOne]);

    // Fetch Location Three based on Location Two selection
    useEffect(() => {
        if (selectedLocationTwo.id === '') return;
        const fetchLocationThree = async () => {
            const response = await API.get(`${BASE_URL}location-twos/${selectedLocationTwo.id}/location-threes`);
            if (response.status === 200) {
                const newObj = { name: 'All', title: 'All', id: '' };

                setLocationThree([newObj, ...response.data]);
            }
        };
        fetchLocationThree();
    }, [selectedLocationTwo]);

    // Fetch Location Four based on Location Three selection
    useEffect(() => {
        if (selectedLocationThree.id === '') return;
        const fetchLocationFour = async () => {
            const response = await API.get(`${BASE_URL}location-threes/${selectedLocationThree.id}/location-fours`);
            if (response.status === 200) {
                const newObj = { name: 'All', title: 'All', id: '' };

                setLocationFour([newObj, ...response.data]);
            }
        };
        fetchLocationFour();
    }, [selectedLocationThree]);





    const [title, setTitles] = useState({ tier1: 'Tier I', tier2: 'Tier II', tier3: 'Tier III', tier4: 'Tier IV' });
    useEffect(() => {

        getLocationConfigs();
    }, [])

    const getLocationConfigs = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);

        if (response.status === 200) {
            const titles = response.data;
            // setTitleData(titles)
            const locationsObject = titles.reduce((obj, item) => {
                obj[item.title] = item.altTitle;

                return obj;
            }, {});
            setTitles({ tier1: locationsObject.LocationOne, tier2: locationsObject.LocationTwo, tier3: locationsObject.LocationThree, tier4: locationsObject.LocationFour })
        }


    }
    const onApply = () => {
        // console.log(startDate,endDate)
        props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, startDate, endDate)
        props.onApplyFilter(true);
    }
    const onCancel = () => {
        props.handleFilter('', '', '', '', null, null)
        setSelectedLocationOne({ name: 'All', title: 'All', id: '' })
        setSelectedLocationTwo({ name: 'All', title: 'All', id: '' })
        setSelectedLocationThree({ name: 'All', title: 'All', id: '' })
        setSelectedLocationFour({ name: 'All', title: 'All', id: '' })
        setStartDate(null)
        setEndDate(null)
        props.onCancelFilter(true);
    }
    useEffect(() => {

    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour])
    return (
        <>

            <div className={'p-card p-3 font-sm'} hidden={''} >
                <h5 className="mb-2">Filter</h5>
                <div className='col-12 mb-3' >
                    <label style={{ fontSize: 16, fontWeight: 500, padding: 10, marginTop: 20 }}>Location</label>
                    <div className='col-12 d-flex flex-column'>

                        <Dropdown value={selectedLocationOne} onChange={(e) => setSelectedLocationOne(e.value)} options={locationOne} optionLabel="name"
                            placeholder={title.tier1} className="m-1" />

                        <Dropdown value={selectedLocationTwo} onChange={(e) => setSelectedLocationTwo(e.value)} options={locationTwo} optionLabel="name"
                            placeholder={title.tier2} className="m-1" />

                        <Dropdown value={selectedLocationThree} onChange={(e) => setSelectedLocationThree(e.value)} options={locationThree} optionLabel="name"
                            placeholder={title.tier3} className="m-1" />

                        <Dropdown value={selectedLocationFour} onChange={(e) => setSelectedLocationFour(e.value)} options={locationFour} optionLabel="name"
                            placeholder={title.tier4} className="m-1" />


                    </div>
                </div>
                <div className='d-flex mb-5' style={{ flexDirection: 'column' }} >
                    <label className="mb-2">Month Range</label>
                    <div className='d-flex justify-content-center' >
                        <div className='col-12'>
                            <Calendar dateFormat='mm/yy' value={startDate} view='month' placeholder='From' className='col-6' onChange={(e) => setStartDate(e.value)} showIcon />
                            <Calendar dateFormat='mm/yy' value={endDate} view='month' placeholder='To' className='col-6' onChange={(e) => setEndDate(e.value)} showIcon />
                        </div> </div>
                </div>
                <div className='d-flex align-items-center' style={{ flexDirection: 'column' }} >
                  
                    <Button label='Apply Filter' className='col-12 mt-2 apply-btn' onClick={() => onApply()} />
                    <Button outlined label='Clear Filter' className='col-12 mt-2 clear-btn' onClick={() => onCancel()} />
                </div>
            </div>
            {/* <div className="row">
                <div className="col">
                    <div className='mb-4 d-flex flex-column '>
                        <label className='mb-2 font-sm'> {title.tier1}</label>
                      

                        <Dropdown value={selectedLocationOne} onChange={(e) => setSelectedLocationOne(e.value)} options={locationOne} optionLabel="name"
                            placeholder="Select" className="" />
                    </div>
                </div>

                <div className="col">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'> {title.tier2}</label>
                       
                        <Dropdown value={selectedLocationTwo} onChange={(e) => setSelectedLocationTwo(e.value)} options={locationTwo} optionLabel="name"
                            placeholder="Select" className="" />
                    </div>
                </div>

                <div className="col">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'> {title.tier3}</label>
                      
                        <Dropdown value={selectedLocationThree} onChange={(e) => setSelectedLocationThree(e.value)} options={locationThree} optionLabel="name"
                            placeholder="Select" className="" />
                    </div>
                </div>

                <div className="col">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'> {title.tier4}</label>
                       

                        <Dropdown value={selectedLocationFour} onChange={(e) => setSelectedLocationFour(e.value)} options={locationFour} optionLabel="name"
                            placeholder="Select" className="" />

                    </div>
                </div>



            </div> */}
        </>
    )
}

export default AllFilterLocation;
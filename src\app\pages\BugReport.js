import React, { useState, useEffect } from 'react';
import { Form, Button, Row, Col } from 'react-bootstrap';
import axios from 'axios';
import CardOverlay from './CardOverlay';
import qs from 'qs';
import { CURRENT_USER_URL, LOCATION1_WITH_ID_URL, SERVICE_NOW_BUG_URL, SERVICE_NOW_TOKEN_URL, SERVICE_NOW_UPLOAD } from '../constants';
import API from '../services/API';
import AllFilterLocation from './AllLocationFilter';
import FilterLocation from './FilterLocation';
import { useSelector } from "react-redux";
import FilterNameLocation from './FilterNameLocation';
import cogoToast from 'cogo-toast';

const BugReport = () => {
    const me = useSelector((state) => state.login.user)

    useEffect(() => { console.log(me) }, [me])
    const [shortDescription, setShortDescription] = useState('');
    const [file, setFile] = useState(null);
    const [requestType, setRequestType] = useState('incident'); // Default to 'incident'
    const [isLoading, setIsLoading] = useState(false);
    const [location, setLocation] = useState({
        locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: ''
    })
    const handleFileChange = (e) => {
        setFile(e.target.files[0]); // Capture the selected file
    };

    const handleRequestTypeChange = (e) => {
        setRequestType(e.target.value); // Update request type based on selection
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        if (requestType !== 'incident') {
            alert('Service Request not enabled')
            return false;
        }
        setIsLoading(true);
        const firstName = me.firstName;
        const email = me.email;

        const desc = `Name: ${firstName}. Email: ${email}. Location: ${location.locationOneId} > ${location.locationTwoId} > ${location.locationThreeId} > ${location.locationFourId}. ${shortDescription}`;

        console.log(desc)
        // const location = 
        const response = await API.get(SERVICE_NOW_TOKEN_URL)
        if (response.status === 200) {

            const access_token = response.data.access_token;
            const ACCESS_TOKEN = localStorage.getItem('access_token')
            const data = {
                desc: desc,
                accessToken: access_token
            };
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${ACCESS_TOKEN}`
                }
            };
            axios.post(SERVICE_NOW_BUG_URL, data, config)
                .then(response => {
                    cogoToast.success('Bug Reported!')
                    setLocation({
                        locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: ''
                    })
                    setShortDescription('')
                    if (!file) {
                        console.error('No file selected.');
                        setFile(null)
                        setIsLoading(false);

                        return;
                    }

                    const sys_id = response.data.result.sys_id;
                    const url = SERVICE_NOW_UPLOAD;
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('sys_id', sys_id);
                    const config = {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authorization': `Bearer ${ACCESS_TOKEN}`
                        }
                    };

                    axios.post(url, formData, config)
                        .then(response => {
                            console.log('File uploaded successfully:', response.data);
                            setFile(null)
                        })
                        .catch(error => {
                            console.error('Error uploading file to incident:', error);
                        });
                    // console.log('Success:', response.data);
                    setIsLoading(false);
                })
                .catch(error => {
                    console.error('Error:', error);
                    setIsLoading(false);
                });
        }
        // // Your existing submit logic here, adjusted to handle both incidents and service requests
        // // and to include the file upload if applicable.

        setIsLoading(false);
    };


    const handleFilter = async (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

        setLocation({ locationOneId: locationOneId, locationTwoId: locationTwoId, locationThreeId: locationThreeId, locationFourId: locationFourId })
    };
    return (
        <CardOverlay title="Report an Issue">
            <div className='row'>
                <div className='col-md-4'>
                    <Form onSubmit={handleSubmit}>
                        <FilterNameLocation disableAll={true} handleFilter={handleFilter} />
                        {/* <AllFilterLocation /> */}
                        <Form.Group className="mb-3" controlId="formShortDescription">
                            <Form.Label>Describe your Issue</Form.Label>
                            <Form.Control
                                as="textarea"
                                rows={3}
                                value={shortDescription}
                                max={100}
                                onChange={(e) => setShortDescription(e.target.value)}
                                placeholder="Describe your issue..."
                                required
                            />
                        </Form.Group>

                        <Form.Group controlId="formFile" className="mb-3">
                            <Form.Label>Attach File</Form.Label>
                            <Form.Control type="file" onChange={handleFileChange} />
                        </Form.Group>

                        <fieldset>
                            <Form.Group as={Row} className="mb-3">
                                <Form.Label as="legend" column sm={3}>
                                    Request Type
                                </Form.Label>
                                <Col sm={10}>
                                    <Form.Check
                                        type="radio"
                                        label="Report a Bug"
                                        value="incident"
                                        name="formHorizontalRadios"
                                        id="formHorizontalRadios1"
                                        checked={requestType === 'incident'}
                                        onChange={handleRequestTypeChange}
                                    />
                                    {/* <Form.Check
                                        type="radio"
                                        label="Service Request"
                                        value="serviceRequest"
                                        name="formHorizontalRadios"
                                        id="formHorizontalRadios2"
                                        checked={requestType === 'serviceRequest'}
                                        onChange={handleRequestTypeChange}
                                    /> */}
                                </Col>
                            </Form.Group>
                        </fieldset>

                        <Button variant="primary" type="submit" disabled={isLoading}>
                            {isLoading ? 'Submitting…' : 'Submit'}
                        </Button>
                    </Form>
                </div>
            </div>
        </CardOverlay>
    );
};

export default BugReport;

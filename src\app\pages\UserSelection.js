import { useState, useEffect } from 'react';
import API from '../services/API';
import { EHS_ROLE_URL, EPTW_ROLE_URL, EXTERNAL_USERS_URL, INCIDENT_ROLE_URL, INSPECTION_ROLE_URL, AUDIT_ROLE_URL, INTERNAL_USERS_URL, LOCATION1_URL, PLANT_ROLE_URL, USERS_URL, USERS_URL_WITH_ID } from '../constants';
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import FilterLocation from './FilterLocation';
import AllFilterLocation from './AllFilterLocation';

const UserSelection = ({ allUsers, allRoles, selectedRole, onUserSelectionChange, updateUserSignal }) => {
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [allRolesInner, setAllRolesInner] = useState({ country: [], ehs: [], eptw: [], incident: [], audit: [], inspection: [], plant: [] })

    useEffect(() => {
        getCountry();
        getEhsRole();
        getEptwRole();
        getIncidentRole();
        getInspectionRole();
        getAuditRole();
        getPlantRole();

    }, [])


    const getCountry = async () => {
        const response = await API.get(LOCATION1_URL)

        if (response.status === 200) {

            setAllRolesInner(p => { return { ...p, country: response.data } })
        }
    }

    const getEhsRole = async () => {
        const response = await API.get(EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRolesInner(p => { return { ...p, ehs: response.data } })
        }
    }

    const getEptwRole = async () => {
        const response = await API.get(EPTW_ROLE_URL)

        if (response.status === 200) {

            setAllRolesInner(p => { return { ...p, eptw: response.data } })
        }
    }

    const getIncidentRole = async () => {
        const response = await API.get(INCIDENT_ROLE_URL)

        if (response.status === 200) {

            setAllRolesInner(p => { return { ...p, incident: response.data } })
        }
    }

    const getInspectionRole = async () => {
        const response = await API.get(INSPECTION_ROLE_URL)

        if (response.status === 200) {

            setAllRolesInner(p => { return { ...p, inspection: response.data } })
        }
    }

    const getAuditRole = async () => {
        const response = await API.get(AUDIT_ROLE_URL)

        if (response.status === 200) {

            setAllRolesInner(p => { return { ...p, audit: response.data } })
        }
    }

    const getPlantRole = async () => {
        const response = await API.get(PLANT_ROLE_URL)

        if (response.status === 200) {

            setAllRolesInner(p => { return { ...p, plant: response.data } })
        }
    }

    useEffect(() => {
        // console.log(allUsers)
        const roleKey = getKeyFromId(selectedRole)
        const filteredUsers = allUsers.filter(obj => obj.hasOwnProperty('customRoles') && obj.customRoles.hasOwnProperty(roleKey) && obj.customRoles[roleKey].includes(selectedRole))
        console.log(filteredUsers)
        setSelectedUsers(filteredUsers)


    }, [selectedRole, allUsers])

    function getKeyFromId(id) {
        for (const key in allRoles) {
            if (Array.isArray(allRoles[key])) {
                const role = allRoles[key].find(role => role.id === id);
                if (role) {
                    return key;
                }
            }
        }
        return null; // Return null if the ID is not found in any array
    }

    function handleCheckboxChange(event, user) {
        if (event.target.checked) {
            setSelectedUsers([...selectedUsers, user]);
        } else {
            setSelectedUsers(selectedUsers.filter((u) => u.id !== user.id));
        }
    }

    function handleSave() {
        const roleKey = getKeyFromId(selectedRole);
        const updateUsers = selectedUsers.map(user => {
            const customRoles = user.customRoles || {};
            const existingRoles = customRoles[roleKey] || [];
            if (!existingRoles.includes(selectedRole)) {
                customRoles[roleKey] = [...existingRoles, selectedRole];
            }
            return {
                id: user.id,
                customRoles: customRoles
            };
        });

        // console.log(updateUsers, 'update');
        onUserSelectionChange(updateUsers);
    }

    function findSelectedRole() {
        for (const key in allRoles) {
            const role = allRoles[key].find(role => role.id === selectedRole);
            if (role) {
                return role;
            }
        }
        return null;
    }

    function filterUsers(users) {
        if (searchQuery.trim() === '') {
            return users;
        }

        return users.filter((user) => {
            return user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                user.email.toLowerCase().includes(searchQuery.toLowerCase());
        });
    }

    const [selectedRoles, setSelectedRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], audit: [], plant: [] })
    const [selectedUserId, setSelectedUserId] = useState({ id: "", email: "", name: "" });
    const [mdShow, setMdShow] = useState(false);

    const openAssignmentModal = async (id, email, name) => {


        const response = await API.get(USERS_URL_WITH_ID(id))
        if (response.status === 200) {

            if (response.data.customRoles)
                setSelectedRoles(response.data.customRoles)
            else
                setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], audit: [], plant: [] })
            setSelectedUserId({ id: id, email: email, name: name })
            setMdShow(true)

        }
    }

    const handleRoleChange = (e, category) => {
        const roleId = e.target.value;
        console.log(roleId)

        setSelectedRoles((prevRoles) => {
            const categoryRoles = prevRoles[category];
            if (e.target.checked) {
                // Add the role to the selected roles
                return {
                    ...prevRoles,
                    [category]: [...categoryRoles, roleId],
                };
            } else {
                // Remove the role from the selected roles
                return {
                    ...prevRoles,
                    [category]: categoryRoles.filter((id) => id !== roleId),
                };
            }
        });
    };

    const handleAssignSubmit = async () => {
        const id = selectedUserId.id;
        const response = await API.patch(USERS_URL_WITH_ID(id), { email: selectedUserId.email, customRoles: selectedRoles })
        if (response.status === 204) {
            setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], audit: [], plant: [] })
            setSelectedUserId({ id: "", email: "", name: "" })
            setMdShow(false)
            cogoToast.info('Assigned', { position: 'top-right' })
            updateUserSignal()

        }
    }

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        // const filteredData = data.filter(item => {
        //   return (
        //     (locationOneId === '' || item.locationOneId === locationOneId) &&
        //     (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
        //     (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
        //     (locationFourId === '' || item.locationFourId === locationFourId)
        //   );
        // });

        // setFilterData(filteredData);
        // setSelectedLocationOne(locationOneId)
        // setSelectedLocationTwo(locationTwoId)
        // setSelectedLocationThree(locationThreeId)
        // setSelectedLocationFour(locationFourId)
    };


    return (
        <>

            <div className='col-6'>


                <div>
                    <h5>Select users for {findSelectedRole()?.name} role:</h5>
                    <input

                        type="text"
                        className='form-control w-50'
                        placeholder="Search users..."
                        value={searchQuery}
                        onChange={(event) => setSearchQuery(event.target.value)}
                    />
                    <ul className='list-style-type-none'>
                        {filterUsers(allUsers).map((user) => (
                            <li key={user.id}>
                                <label>
                                    <input
                                        type="checkbox"
                                        checked={selectedUsers.some((u) => u.id === user.id)}
                                        onChange={(event) => handleCheckboxChange(event, user)}
                                    />
                                    &nbsp; {user.firstName} ({user.email})
                                </label>
                                <i class="mdi mdi-text-box-check-outline text-danger cursor-pointer" onClick={(e) => openAssignmentModal(user.id, user.email)}></i>
                            </li>
                        ))}
                    </ul>
                    <button className="btn btn-primary" onClick={handleSave}>Save Selection</button>



                </div>
            </div>

            <div className='col-6'>
                <h5>Selected Users <button className='btn btn-light ms-2'> <i className='mdi mdi-export'></i> Export</button></h5>
                {
                    selectedUsers.map(i => {
                        return (
                            <div key={i.id}>
                                <p>{i.firstName} ({i.email} - {i.type === 'Internal' ? 'STT GDC' : i.company ? i.company : 'External'})  <i class="mdi mdi-text-box-check-outline text-danger cursor-pointer" onClick={(e) => openAssignmentModal(i.id, i.email, i.firstName)}></i></p>

                            </div>
                        )
                    })
                }
            </div>
            <Modal
                show={mdShow}
                size="lg"
                onHide={() => setMdShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    Assign Permissions to {selectedUserId.name}
                </Modal.Header>

                <Modal.Body>
                    <form className="forms">


                        <h4>EHS Observation</h4>
                        <div className='form-group mb-2'>

                            {
                                allRolesInner.ehs && allRolesInner.ehs.slice() // Create a shallow copy of the array to avoid mutating the original array.
                                    .sort((a, b) => {
                                        // Check if 'View Only' is in the name, and sort accordingly.
                                        const nameA = a.name.toLowerCase();
                                        const nameB = b.name.toLowerCase();
                                        if (nameA.includes('view only') && !nameB.includes('view only')) {
                                            return -1; // 'a' should come before 'b'.
                                        }
                                        if (!nameA.includes('view only') && nameB.includes('view only')) {
                                            return 1; // 'a' should come after 'b'.
                                        }
                                        return 0; // No change in order.
                                    }).map((i, k) => {
                                        return (
                                            <label className='me-3' key={k}>
                                                <input value={i.id} checked={selectedRoles.ehs?.includes(i.id)} onChange={(e) => handleRoleChange(e, 'ehs')} type='checkbox' /> {i.name}
                                            </label>
                                        )
                                    })
                            }

                        </div>
                        <AllFilterLocation handleFilter={handleFilter} disableAll={true} period={false} />
                        <h4>ePermit to Work</h4>
                        <div className='form-group mb-2'>

                            {
                                allRolesInner.eptw && allRolesInner.eptw.slice() // Create a shallow copy of the array to avoid mutating the original array.
                                    .sort((a, b) => {
                                        // Check if 'View Only' is in the name, and sort accordingly.
                                        const nameA = a.name.toLowerCase();
                                        const nameB = b.name.toLowerCase();
                                        if (nameA.includes('view only') && !nameB.includes('view only')) {
                                            return -1; // 'a' should come before 'b'.
                                        }
                                        if (!nameA.includes('view only') && nameB.includes('view only')) {
                                            return 1; // 'a' should come after 'b'.
                                        }
                                        return 0; // No change in order.
                                    }).map((i, k) => {
                                        return (
                                            <label className='me-3' key={k}>
                                                <input value={i.id} checked={selectedRoles.eptw?.includes(i.id)} onChange={(e) => handleRoleChange(e, 'eptw')} type='checkbox' /> {i.name}
                                            </label>
                                        )
                                    })
                            }

                        </div>
                        <h4>Incident Reporting</h4>
                        <div className='form-group mb-2'>

                            {
                                allRolesInner.incident && allRolesInner.incident.slice() // Create a shallow copy of the array to avoid mutating the original array.
                                    .sort((a, b) => {
                                        // Check if 'View Only' is in the name, and sort accordingly.
                                        const nameA = a.name.toLowerCase();
                                        const nameB = b.name.toLowerCase();
                                        if (nameA.includes('view only') && !nameB.includes('view only')) {
                                            return -1; // 'a' should come before 'b'.
                                        }
                                        if (!nameA.includes('view only') && nameB.includes('view only')) {
                                            return 1; // 'a' should come after 'b'.
                                        }
                                        return 0; // No change in order.
                                    }).map((i, k) => {
                                        return (
                                            <label className='me-3' key={k}>
                                                <input value={i.id} checked={selectedRoles.incident?.includes(i.id)} onChange={(e) => handleRoleChange(e, 'incident')} type='checkbox' /> {i.name}
                                            </label>
                                        )
                                    })
                            }

                        </div>
                        <h4>Inspection</h4>
                        <div className='form-group mb-2'>

                            {
                                allRolesInner.inspection && allRolesInner.inspection.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.inspection?.includes(i.id)} onChange={(e) => handleRoleChange(e, 'inspection')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>

                        <h4>Audit</h4>
                        <div className='form-group mb-2'>

                            {
                                allRolesInner.audit && allRolesInner.audit.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.audit?.includes(i.id)} onChange={(e) => handleRoleChange(e, 'audit')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>


                        <h4>Plant and Equipment</h4>
                        <div className='form-group mb-2'>

                            {
                                allRolesInner.plant && allRolesInner.plant.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.plant?.includes(i.id)} onChange={(e) => handleRoleChange(e, 'plant')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <>
                        <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>

                        {selectedUserId.id && <Button variant="primary" onClick={handleAssignSubmit}>Assign</Button>}

                    </>


                </Modal.Footer>
            </Modal>
        </>
    );
}

export default UserSelection;
import React, { useState, useEffect } from "react";
import API from "../services/API";
import { LOCATION1_URL, LOCATION_TIER1_URL, TIER1_TIER2_URL, TIER2_TIER3_URL, DYNAMIC_TITLES_URL } from "../constants";


const FilterNameLocation = (props) => {

    const [selectedLocationOne, setSelectedLocationOne] = useState({ id: '', name: '' });
    const [selectedLocationTwo, setSelectedLocationTwo] = useState({ id: '', name: '' });
    const [selectedLocationThree, setSelectedLocationThree] = useState({ id: '', name: '' });
    const [selectedLocationFour, setSelectedLocationFour] = useState({ id: '', name: '' });


    const [locationOne, setLocationOne] = useState([]);
    useEffect(() => {

        getLocationOne()
    }, [])
    const getLocationOne = async () => {


        const response = await API.get(LOCATION1_URL);
        if (response.status === 200) {

            setLocationOne(response.data)
            setSelectedLocationTwo('');
            setSelectedLocationThree('');
            setSelectedLocationFour('');
        }
    }





    const [locationTwo, setLocationTwo] = useState([]);
    useEffect(() => {
        if (selectedLocationOne && selectedLocationOne.id)
            getLocationOneTwo();
        else {
            setSelectedLocationTwo('');
            setSelectedLocationThree('');
            setSelectedLocationFour('');
        }
    }, [selectedLocationOne])

    const getLocationOneTwo = async () => {


        const response = await API.get(LOCATION_TIER1_URL(selectedLocationOne.id));
        if (response.status === 200) {

            setLocationTwo(response.data)
            setSelectedLocationTwo('');
            setSelectedLocationThree('');
            setSelectedLocationFour('');
        }
    }


    const [locationThree, setLocationThree] = useState([]);
    useEffect(() => {
        if (selectedLocationTwo && selectedLocationTwo.id)
            getLocationTwoThree();
        else {

            setSelectedLocationThree('');
            setSelectedLocationFour('');
        }
    }, [selectedLocationTwo])

    const getLocationTwoThree = async () => {


        const response = await API.get(TIER1_TIER2_URL(selectedLocationTwo.id));
        if (response.status === 200) {

            setLocationThree(response.data)
            setSelectedLocationThree('');
            setSelectedLocationFour('');
        }
    }


    const [locationFour, setLocationFour] = useState([]);
    useEffect(() => {
        if (selectedLocationThree && selectedLocationThree.id)
            getLocationThreeFour();
        else {

            setSelectedLocationFour('');
        }
    }, [selectedLocationThree])

    const getLocationThreeFour = async () => {


        const response = await API.get(TIER2_TIER3_URL(selectedLocationThree.id));
        if (response.status === 200) {

            setLocationFour(response.data)
            setSelectedLocationFour('');
        }
    }



    const [title, setTitles] = useState({ tier1: 'Tier I', tier2: 'Tier II', tier3: 'Tier III', tier4: 'Tier IV' });
    useEffect(() => {

        getLocationConfigs();
    }, [])

    const getLocationConfigs = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);

        if (response.status === 200) {
            const titles = response.data;
            // setTitleData(titles)
            const locationsObject = titles.reduce((obj, item) => {
                obj[item.title] = item.altTitle;

                return obj;
            }, {});
            setTitles({ tier1: locationsObject.LocationOne, tier2: locationsObject.LocationTwo, tier3: locationsObject.LocationThree, tier4: locationsObject.LocationFour })
        }


    }

    useEffect(() => {
        props.handleFilter(
            selectedLocationOne.name,
            selectedLocationTwo.name,
            selectedLocationThree.name,
            selectedLocationFour.name)
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour])
    return (
        <>
            <div className="row">
                <div className="col-12">
                    <div className='mb-4'>
                        <label className='mb-2 font-sm'> {title.tier1}</label>
                        <select className='form-select' onChange={(e) => {
                            const selectedIndex = e.target.options.selectedIndex;
                            const id = e.target.options[selectedIndex].value;
                            const name = e.target.options[selectedIndex].text;
                            setSelectedLocationOne({ id, name });
                        }} >
                            <option value={''}>{props.disableAll ? 'Choose' : 'All'} {title.tier1}</option>

                            {
                                locationOne.map(i => {
                                    return <option key={i.id} value={i.id}>{i.name}</option>

                                })
                            }

                        </select>
                    </div>
                </div>

                {(selectedLocationOne && selectedLocationOne.id) && selectedLocationOne !== 'tier1-all' && <div className="col-12">
                    <div className='mb-4'>
                        <label className='mb-2 font-sm'> {title.tier2}</label>
                        <select
                            className='form-select'
                            onChange={(e) => {
                                const selectedIndex = e.target.options.selectedIndex;
                                const id = e.target.options[selectedIndex].value;
                                const name = e.target.options[selectedIndex].text;
                                setSelectedLocationTwo({ id, name });
                            }}

                        >
                            <option value={''}>{props.disableAll ? 'Choose' : 'All'} {title.tier2}</option>

                            {
                                locationTwo.map(i => {
                                    return <option key={i.id} value={i.id}>{i.name}</option>

                                })
                            }
                        </select>
                    </div>
                </div>}

                {(selectedLocationTwo && selectedLocationTwo.id) && selectedLocationTwo !== 'tier2-all' && <div className="col-12">
                    <div className='mb-4'>
                        <label className='mb-2 font-sm'> {title.tier3}</label>
                        <select
                            className='form-select'
                            onChange={(e) => {
                                const selectedIndex = e.target.options.selectedIndex;
                                const id = e.target.options[selectedIndex].value;
                                const name = e.target.options[selectedIndex].text;
                                setSelectedLocationThree({ id, name });
                            }}

                        >
                            <option value={''}>{props.disableAll ? 'Choose' : 'All'} {title.tier3}</option>

                            {
                                locationThree.map(i => {
                                    return <option key={i.id} value={i.id}>{i.name}</option>

                                })
                            }
                        </select>
                    </div>
                </div>}

                {(selectedLocationThree && selectedLocationThree.id) && selectedLocationThree !== 'tier3-all' && <div className="col-12">
                    <div className='mb-4'>
                        <label className='mb-2 font-sm'> {title.tier4}</label>
                        <select
                            className='form-select'
                            onChange={(e) => {
                                const selectedIndex = e.target.options.selectedIndex;
                                const id = e.target.options[selectedIndex].value;
                                const name = e.target.options[selectedIndex].text;
                                setSelectedLocationFour({ id, name });
                            }}

                        >
                            <option value={''}>{props.disableAll ? 'Choose' : 'All'} {title.tier4}</option>

                            {
                                locationFour.map(i => {
                                    return <option key={i.id} value={i.id}>{i.name}</option>

                                })
                            }
                        </select>
                    </div>
                </div>
                }

            </div>
        </>
    )
}

export default FilterNameLocation;
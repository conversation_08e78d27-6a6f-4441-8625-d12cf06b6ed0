import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON>r, Legend, LabelList } from 'recharts';

const BarFour = () => {
    const data = [
        { month: 'Jan', Safety: 10, Health: 20, Environment: 15 },
        { month: 'Feb', Safety: 15, Health: 25, Environment: 18 },
        { month: 'Mar', Safety: 20, Health: 22, Environment: 20 },
        { month: 'Apr', Safety: 18, Health: 18, Environment: 22 },
        { month: 'May', Safety: 25, Health: 15, Environment: 25 },
        { month: 'Jun', Safety: 30, Health: 12, Environment: 28 },
        { month: 'Jul', Safety: 22, Health: 10, Environment: 30 },
        { month: 'Aug', Safety: 28, Health: 8, Environment: 32 },
        { month: 'Sep', Safety: 20, Health: 15, Environment: 35 },
        { month: 'Oct', Safety: 18, Health: 18, Environment: 38 },
        { month: 'Nov', Safety: 15, Health: 22, Environment: 40 },
        { month: 'Dec', Safety: 12, Health: 25, Environment: 42 },
    ];

    return (
        <ResponsiveContainer width="100%" height={500}>
            <BarChart data={data}>
                <XAxis dataKey="month" />
                <YAxis tickFormatter={() => ''} />
                <Tooltip />
                
                {/* Stacked bars for Safety, Health, Environment */}
                <Bar stackId="a" dataKey="Safety" fill="#C0FFDC" radius={[0, 0, 0, 0]} barSize={30}>
                    <LabelList dataKey="Safety" position="top" />
                </Bar>
                <Bar stackId="a" dataKey="Health" fill="#7BD4C2" radius={[0, 0, 0, 0]} barSize={30}>
                    <LabelList dataKey="Health" position="top" />
                </Bar>
                <Bar stackId="a" dataKey="Environment" fill="#00B08C" radius={[10, 10, 0, 0]} barSize={30}>
                    <LabelList dataKey="Environment" position="top" />
                </Bar>

                <Legend verticalAlign="bottom" align="center" />
            </BarChart>
        </ResponsiveContainer>
    );
};

export default BarFour;

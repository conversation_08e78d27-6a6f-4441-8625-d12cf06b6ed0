import React,{useState} from 'react'
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
function TableHeader({ onDateSearch,setFilterData }) {
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    
    // console.log(filters)


    // const value = filters['global'] ? filters['global'].value : '';

    return (
        <div className='d-flex justify-content-start mb-5'>
            <div className="">
                <label className='me-3 mb-2'>Month Filter</label>
                <div>

                <Calendar view='month' className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="mm/yy" showIcon />
                    <Calendar view='month' className="w-full  me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="mm/yy" showIcon />

                    <Button label='Apply' className='me-3' text raised severity="success" aria-label="Search" onClick={() => onDateSearch()} />
                    <Button label='Clear' text raised severity="danger" aria-label="Cancel" onClick={() => { setFilterData(); setStartDate(null); setEndDate(null) }}/>
                </div>


            </div>

            {/* <span className="p-input-icon-left">
                <i className="fa fa-search" />
                <InputText type="search" value={value || ''}  placeholder="Global Search" />
            </span> */}
        </div>
    );


}

export default TableHeader
import React, { useEffect, useState, useMemo } from "react";
import moment from 'moment';
import { Modal } from 'react-bootstrap';
import API from "../services/API";
import { INSPECTOR_USERS_URL, CHECKLIST_URL, GET_USERS_BY_ROLE } from "../constants";
import ReactDatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";

import AllLocationFilter from './AllLocationFilter'
import { useSelector } from "react-redux";

const AuditForm = ({ show, onHide, formData, onSubmit, reSubmit, deleteSubmit }) => {
    console.log(formData)
    const me = useSelector((state) => state.login.user)
    const isAuditorScheduler = useMemo(() => {
        return me?.validationRoles?.some(item => item.name === 'Audit Scheduler') || false;
    }, [me]);

    console.log(isAuditorScheduler)
    const [location, setLocation] = useState(
        {
            locationOneId: '',
            locationTwoId: '',
            locationThreeId: '',
            locationFourId: ''
        }
    )
    // Local state for form inputs
    const [formState, setFormState] = useState({
        month: '',
        checklistId: '',
        assignedToId: '',
        name: '',
        dateTime: '',
        endDateTime: '',
        created: moment(new Date()).format('DD/MM/YYYY'),
        locationOneId: '',
        locationTwoId: '',
        locationThreeId: '',
        locationFourId: ''
    });
    // const startDate = moment(`${month} ${year}`, 'MMM YYYY').startOf('month').toDate();
    // const endDate = moment(`${month} ${year}`, 'MMM YYYY').endOf('month').toDate();

    const [inspector, setInspector] = useState([])
    const [limit, setLimit] = useState(null)

    useEffect(() => {
        // getInspector()
        getChecklist()
    }, [])
    useEffect(() => {
        if (formData && Object.keys(formData).length > 0) {
            setFormState({
                month: formData.month || '',
                checklistId: formData.checklistId || '',
                assignedToId: formData.assignedToId || '',
                name: formData.name || '',
                dateTime: formData.dateTime || '',
                endDateTime: formData.endDateTime || '',
                created: formData.created || moment(new Date()).format('DD/MM/YYYY'),
                locationOneId: formData.locationOneId || '',
                locationTwoId: formData.locationTwoId || '',
                locationThreeId: formData.locationThreeId || '',
                locationFourId: formData.locationFourId || ''
            });

            setLocation({
                locationOneId: formData.locationOneId || '',
                locationTwoId: formData.locationTwoId || '',
                locationThreeId: formData.locationThreeId || '',
                locationFourId: formData.locationFourId || ''
            });
        }
    }, [formData]);

    const [checklistData, setChecklistData] = useState([]);


    const getChecklist = async () => {
        const response = await API.get(CHECKLIST_URL);
        if (response.status === 200) {
            setChecklistData(response.data.filter(i => i.application === 'Audit').map(i => ({ ...i, monthlyStatus: new Array(12).fill('') })))
        }
        return [];
    }
    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        console.log(locationOneId, locationTwoId, locationThreeId, locationFourId)
        setLocation({ locationOneId: locationOneId, locationTwoId: locationTwoId, locationThreeId: locationThreeId, locationFourId: locationFourId })
        setFormState(prev => ({ ...prev, locationOneId: locationOneId, locationTwoId: locationTwoId, locationThreeId: locationThreeId, locationFourId: locationFourId }));

    };
    useEffect(() => {
        if (Object.values(location).some(id => id !== '')) {
            getInspector();
        }
    }, [location]);


    const handleDateChange = (date) => {
        setFormState(prev => ({ ...prev, dateTime: moment(date).format('DD/MM/YYYY') }));
        setLimit(date)

    };
    const handleEndDateChange = (date) => {
        setFormState(prev => ({ ...prev, endDateTime: moment(date).format('DD/MM/YYYY') }));
    };
    const getInspector = async () => {
        try {
            const response = await API.post(GET_USERS_BY_ROLE, {
                locationOneId: location.locationOneId,
                locationTwoId: location.locationTwoId,
                locationThreeId: location.locationThreeId,
                locationFourId: location.locationFourId,
                mode: 'auditor'
            });

            if (response.status === 200) {
                setInspector(response.data);
            } else {
                console.error(`Error: Received status code ${response.status}`);
            }
        } catch (error) {
            console.error('Error fetching inspector data:', error);
        }
    };
    const handleSubmit = () => {
        // You might want to validate the formState here before calling onSubmit
        setFormState((prev) => { return { ...prev, month: '' } })
        onSubmit(formState);
        onHide(); // Hide the modal after submit
    };
    const handleReSubmit = () => {
        // You might want to validate the formState here before calling onSubmit
        setFormState((prev) => { return { ...prev, month: '' } })
        reSubmit(formState);
        onHide(); // Hide the modal after submit
    };
    const handleDeleteSubmit = () => {
        // You might want to validate the formState here before calling onSubmit

        deleteSubmit(formState);
        onHide(); // Hide the modal after submit
    };
    const getLastMonthSameDate = (date) => {
        const lastMonthDate = new Date(date);
        lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);
        if (lastMonthDate.getDate() !== date.getDate()) {
            lastMonthDate.setDate(0);
        }
        return lastMonthDate;
    };

    const minDate = getLastMonthSameDate(new Date());
    return (
        <Modal show={show} onHide={onHide}>
            <Modal.Header closeButton>
                <Modal.Title>Schedule Audit </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {/* Your form inputs go here */}

                <form>
                    {/* Assume you have a form state setup to handle these inputs */}

                    {(formData && Object.keys(formData).length > 0) ? <>
                        <div className="form-group">
                            <label>Type of Audit </label>
                            <select className="form-select" disabled value={formState.checklistId} onChange={(e) => setFormState(prev => { return { ...prev, checklistId: e.target.value } })}>
                                <option value="">Choose ...  </option>
                                {
                                    checklistData.map(i => {
                                        return <option key={i.id} value={i.id}>{i.name}</option>
                                    })
                                }
                            </select>
                        </div>
                        <div className="form-group">
                            <label>Location </label>
                            <input type="text" className="form-control" value={formData.locationOne.name+' > '+ formData.locationTwo.name+' > '+formData.locationThree.name+' > '+formData.locationFour.name} disabled />
                        </div>

                    </> : <>
                        <div className="form-group">
                            <label>Type of Audit </label>
                            {/* <p>{checklist.name}</p> */}
                            <select className="form-select" value={formState.checklistId} onChange={(e) => setFormState(prev => { return { ...prev, checklistId: e.target.value } })}>
                                <option value="">Choose ...  </option>
                                {
                                    checklistData.map(i => {
                                        return <option key={i.id} value={i.id}>{i.name}</option>
                                    })
                                }
                            </select>

                        </div>
                        <AllLocationFilter handleFilter={handleFilter} getLocation={formData} />


                    </>}
                    <div className="form-group">
                        <label> Audit Dates </label>

                        <div className="row">
                            <div className="col-6">
                                <label>From</label>
                                <ReactDatePicker
                                    className="form-control"
                                    onChange={handleDateChange}
                                    minDate={minDate}
                                    dateFormat="dd/MM/yyyy"
                                    value={formState.dateTime}
                                />
                            </div>
                            <div className="col-6">
                                <label>To</label>
                                <ReactDatePicker
                                    className="form-control"
                                    onChange={handleEndDateChange}
                                    minDate={limit}
                                    dateFormat="dd/MM/yyyy"
                                    value={formState.endDateTime}
                                />
                            </div>
                        </div>

                    </div>
                    <div className="form-group">
                        <label>Auditor</label>
                        <select onChange={(e) => setFormState(prev => { return { ...prev, assignedToId: e.target.value } })} value={formState.assignedToId} className="form-select">
                            <option value="">Assign Auditor</option>
                            {
                                inspector.map(i => {
                                    return <option key={i.id} value={i.id}>{i.firstName}</option>
                                })
                            }
                        </select>
                    </div>

                    {/* Add other inputs here */}
                </form>
            </Modal.Body>
            <Modal.Footer>
                {/* <button className="btn btn-light" onClick={onHide}>Close</button> */}
                {(formData && Object.keys(formData).length > 0) ? <>
                   
                        <button className="btn btn-secondary" onClick={handleDeleteSubmit}>Cancel Schedule</button>

                        <button className="btn btn-primary" onClick={handleReSubmit}>Re-Schedule</button>
                
                </>

                    :
                    <button className="btn btn-primary" onClick={handleSubmit}>Schedule</button>
                }
            </Modal.Footer>
        </Modal>
    );
};


export default AuditForm;
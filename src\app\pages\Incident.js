import React, { useState, useMemo, useEffect } from 'react'
import AllIncidentReport  from './AllIncidentReport';
import IncidentReport from './IncidentReport';
import IncidentReview from './IncidentReview';
import IncidentInvestigation from './IncidentInvestigation';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';

import { useSelector } from "react-redux";
import BarChart from '../dashboard/BarChart';
import WordChart from '../dashboard/WordChart';
import CardOverlay from './CardOverlay';
import AllFilterLocation from './AllLocationFilter';
import UnderIncidentInvestigation from './UnderIncidentInvesitgation';
import API from '../services/API';
import { ACTION_URL, REPORT_INCIDENT_REVIEW_URL, REPORT_INCIDENT_LEAD_INVESTIGATOR_URL, ALL_INCIDENT_URL, REPORT_INCIDENT_INVESTIGATE_URL, GET_REVIEWER_INCIDENT, GET_REPORTER_INCIDENT, GET_INCIDENT_OWNER_INCIDENT } from '../constants';
import ActionCard from './ActionCard';
import IncidentInvestigationCard from './IncidentInvestigationCard';
import IncidentCardReport from './IncidentCardReport';
import AppSwitch from './AppSwitch';
import moment from 'moment';
import AllFilterLocationVertical from './AllFilterLocationVertical'
import { InputText } from 'primereact/inputtext';
import extractImpactLevel from '../utils/impactLevel';


function CustomTabPanel(props) {
  const { children, value, tabValue, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== tabValue}
      id={`incident-tabpanel-${tabValue}`}
      aria-labelledby={`incident-tab-${tabValue}`}
      {...other}
    >
      {value === tabValue && (
        <Box >
          {children}
        </Box>
      )}
    </div>
  );
}

const customFontStyle = {
  fontFamily: 'Lato, sans-serif',
  display: "flex",
  alignItems: 'center',
  justifyContent: 'center'
};

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `incident-tab-${index}`,
    'aria-controls': `incident-tabpanel-${index}`,
  };
}


const Incident = () => {


  const [showFilter, setShowFilter] = useState(true)
  const [applyFilter, setApplyFilter] = useState(false)
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)
  const [clear, setClear] = useState(true)
  const [search, setSearch] = useState('')

  const [filter, setFilter] = useState([])

  const [dates, setDates] = useState([])
  const [locationOneId, setlocationOneId] = useState('')
  const [locationTwoId, setlocationTwoId] = useState('')
  const [locationThreeId, setlocationThreeId] = useState('')
  const [locationFourId, setlocationFourId] = useState('')


  const onApplyFilter = (type) => {
    setApplyFilter(type)
    setShowFilter(true)
  }

  const onCancelFilter = (type) => {
    setApplyFilter(false)
    setShowFilter(true)
    setClear(!clear)
  }



  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate) => {



    setDates([])
    const data = [
      locationOneId.name || '',
      locationTwoId.name || '',
      locationThreeId.name || '',
      locationFourId.name || '',

    ];

    if (startDate !== null && endDate !== null) {
      const date = [
        moment(startDate).format('MMM YYYY'),
        moment(endDate).format('MMM YYYY')
      ]
      setDates(date)
    }


    setFilter(data)
    setSearch('')

    setlocationOneId(locationOneId.id)
    setlocationTwoId(locationTwoId.id)
    setlocationThreeId(locationThreeId.id)
    setlocationFourId(locationFourId.id)
    setStartDate(startDate)
    setEndDate(endDate)
  };
  useEffect(() => {

    const filterData = (data) => {
      return data.filter(item => {
        return (
          (!locationOneId || item.locationOneId === locationOneId) &&
          (!locationTwoId || item.locationTwoId === locationTwoId) &&
          (!locationThreeId || item.locationThreeId === locationThreeId) &&
          (!locationFourId || item.locationFourId === locationFourId) &&
          (!startDate || !endDate || isBetweenDateRange(item.created, moment(startDate).startOf('month'), moment(endDate).endOf('month')))

        );
      });
    };

    // const filteredData = filterData(allIncidentFilter, locationOneId, locationTwoId, locationThreeId, locationFourId);
    const filteredAllIncident = filterData(allIncidentFilter);
    const filteredIncidentReview = filterData(incidentReviewFilter).filter(item => item.status === 'Reported');
    const filteredIncidentInves = filterData(incidentInvesFilter);
    const filteredIncidentInvesToBe = filterData(incidentInvesToBeFilter);

    setAllIncident(filteredAllIncident);
    setIncidentReview(filteredIncidentReview);
    setIncidentInves(filteredIncidentInves);
    setIncidentInvesToBe(filteredIncidentInvesToBe);

    // Set the FilterData variables for use in search when applyFilter is true
    setAllIncidentFilterData(filteredAllIncident);
    setIncidentReviewFilterData(filteredIncidentReview);
    setIncidentInvesFilterData(filteredIncidentInves);
    setIncidentInvesToBeFilterData(filteredIncidentInvesToBe);


  }, [locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate])
  useEffect(() => {
    if (!applyFilter) {
      setAllIncident(allIncidentFilter.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())))
      setIncidentReview(incidentReviewFilter.filter(item =>
        item.maskId.toLowerCase().includes(search.toLowerCase()) && item.status === 'Reported'
      ));
      setIncidentInves(incidentInvesFilter.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())));
      setIncidentInvesToBe(incidentInvesToBeFilter.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())));
    } else {
      // setObsData(finalObs.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())))
      // setObsDataOther(finalObsOther.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())));
      setAllIncident(allIncidentFilterData.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())))
      setIncidentReview(incidentReviewFilterData.filter(item =>
        item.maskId.toLowerCase().includes(search.toLowerCase()) && item.status === 'Reported'
      ));
      setIncidentInves(incidentInvesFilterData.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())));
      setIncidentInvesToBe(incidentInvesToBeFilterData.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())));
    }

  }, [search])


  const me = useSelector((state) => state.login.user)



  const isIncidentReview = useMemo(() => {
    return me?.validationRoles?.some(item =>  item.name === 'Group EHS Team' || item.name === 'Country EHS Director' || item.name === 'Incident Owner') || false;
  }, [me]);


  const isIncidentTrigger = useMemo(() => {
    return me?.validationRoles?.some(item => item.name?.includes('Incident Owner') || item.name === 'Group EHS Team' || item.name === 'Country EHS Director') || false;
  }, [me]);

  const isIncidentInvestigate = useMemo(() => {
    return me?.validationRoles?.some(item => item.name === 'Lead Investigator') || false;
  }, [me]);

  const isIncidentUnderInvestigation = useMemo(() => {
    return me?.validationRoles?.some(item => item.name?.includes('Incident Owner') || item.name === 'Group EHS Team' || item.name === 'Country CEO' || item.name === 'Country EHS Director') || false;
  }, [me]);

  const [value, setValue] = useState(0);
  const [topLevelValue, setTopLevelValue] = useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleTopLevelChange = (event, newValue) => {
    setTopLevelValue(newValue);
  };



  const TABS = {
    ALL: "ALL",
    REVIEW: "REVIEW",
    INVESTIGATION: "INVESTIGATION",
    UNDER_INVESTIGATION: "UNDER_INVESTIGATION",

  };

  const TOP_LEVEL_TABS = {
    DASHBOARD: "DASHBOARD",
    ALL: "ALL",
    ACTION: "ACTION",
    REVIEW: "REVIEW",
    UNDER_INVESTIGATION: "UNDER_INVESTIGATION",
  };

  useEffect(() => {
    setValue(TABS.ALL)
    setTopLevelValue(TOP_LEVEL_TABS.ACTION)
  }, [])
  const [actionData, setActionData] = useState([]);
  const [rendered, setRendered] = useState(0)
  const [allIncident, setAllIncident] = useState([])
  const [allIncidentFilter, setAllIncidentFilter] = useState([])
  const [allIncidentFilterData, setAllIncidentFilterData] = useState([])
  const [incidentReviewFilterData, setIncidentReviewFilterData] = useState([])
  const [incidentInvesFilterData, setIncidentInvesFilterData] = useState([])
  const [incidentInvesToBeFilterData, setIncidentInvesToBeFilterData] = useState([])
  const [incidentReview, setIncidentReview] = useState([])
  const [incidentReviewFilter, setIncidentReviewFilter] = useState([])
  const [incidentInves, setIncidentInves] = useState([])
  const [incidentInvesFilter, setIncidentInvesFilter] = useState([])
  const [incidentInvesToBe, setIncidentInvesToBe] = useState([])
  const [incidentInvesToBeFilter, setIncidentInvesToBeFilter] = useState([])
  const [combinedData, setCombinedData] = useState([]);

  useEffect(() => {

    getActionData();
    // getIncidentData();
    // getIncidentReview();
    getCombinedIncidentData()
    getIncidentInves();
    getIncidentToBeData();
    fetchData()
  }, [])

  useEffect(() => {
    fetchData();
  }, [rendered]);

  const fetchData = async () => {
    try {
      const [actionDataResponse, incidentReviewResponse, investigationResponse, incidentReporter, triggerIncident] = await Promise.all([
        fetchActionData(), // Assuming there's a function or API to fetch this data
        fetchIncidentReviewData(),
        fetchInvestigationData(),
        fetchIncidentReporterData(),
        fetchTriggerIncidentData()
      ]);
      console.log(actionDataResponse, 'actionDataResponse', incidentReviewResponse, 'incidentReviewResponse', investigationResponse, 'investigationResponse', incidentReporter, 'incidentReporter', triggerIncident, 'triggerIncident')
      // Assuming all responses are in the correct format and do not require transformation
      setCombinedData([...actionDataResponse, ...incidentReviewResponse, ...investigationResponse, ...incidentReporter, ...triggerIncident]);

    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const fetchTriggerIncidentData = async () => {
    const response = await API.get(GET_INCIDENT_OWNER_INCIDENT);

    return response.status === 200 && isIncidentTrigger ? response.data.filter(i => i.status === 'Reviewed').map(i => {
      return {
        actionType: 'trigger_incident',
        application: 'INCIDENT',
        applicationDetails: i,
        createdDate: i.created,
        description: '',
        objectId: i.id
      }
    }) : [];
  };

  const fetchActionData = async () => {
    // Dummy API call, replace with actual endpoint
    let [action, review, invert] = [[], [], []]


    const response = await API.get(ACTION_URL);
    if (response.status === 200) {


      action = getFilteredActions('INCIDENT', ['open'], response.data)
      console.log(action, 'action data')
    }
    setActionData(action)
    return action;
  };

  const fetchIncidentReviewData = async () => {


    const response = await API.get(GET_REVIEWER_INCIDENT);

    // Check if user has Incident Reviewer role in addition to isIncidentReview
    const hasIncidentReviewerRole = me?.validationRoles?.some(item => item.name === 'Incident Reviewer') || false;

    return response.status === 200 && (isIncidentReview || hasIncidentReviewerRole)
      ? response.data
        .filter(i => i.status === 'Reported')  // Filter for status 'Reported'
        .map(i => ({
          actionType: 'review_incident',
          application: 'INCIDENT',
          applicationDetails: i,
          createdDate: i.created,
          description: '',
          objectId: i.id
        }))
      : [];
  };

  const fetchIncidentReporterData = async () => {


    const response = await API.get(GET_REPORTER_INCIDENT);

    return response.status === 200 && response.data
      .filter(i => i.status === 'Reported')  // Filter for status 'Reported'
      .map(i => ({
        actionType: 'modify_incident',
        application: 'INCIDENT',
        applicationDetails: i,
        createdDate: i.created,
        description: '',
        objectId: i.id
      }));
  };

  const fetchInvestigationData = async () => {
    const params = { "include": [{ "relation": "user" }] };
    const response = await API.get(`${REPORT_INCIDENT_LEAD_INVESTIGATOR_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);



    return response.status === 200 ? response.data.filter(i => i.status !== 'Investigation Completed').map(i => {
      return {
        actionType: 'conduct_investigation',
        application: 'INCIDENT',
        applicationDetails: i,
        createdDate: i.created,
        description: i.investigationRemarks,
        objectId: i.id
      }
    }) : [];
  };


  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }
  const getActionData = async () => {




  }

  const getCombinedIncidentData = async () => {
    const incidentParams = {
      include: [
        { relation: "user" },
        { relation: "incidentOwner" },
        { relation: "investigator" },
        { relation: "reviewer" }
      ]
    };

    const reviewParams = {
      include: [{ relation: "user" }]
    };

    try {
      // Fetch both API responses concurrently
      const [incidentResponse, reviewResponse, reporterResponse] = await Promise.all([
        API.get(`${ALL_INCIDENT_URL}?filter=${encodeURIComponent(JSON.stringify(incidentParams))}`),
        API.get(`${GET_REVIEWER_INCIDENT}?filter=${encodeURIComponent(JSON.stringify(reviewParams))}`),
        API.get(`${GET_REPORTER_INCIDENT}?filter=${encodeURIComponent(JSON.stringify(reviewParams))}`)
      ]);

      if (incidentResponse.status === 200 && reviewResponse.status === 200) {
        // Process the first API response
        const incidentData = incidentResponse.data.map((item) => ({
          ...item,
          incidentDate: moment(item.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY')
        }));

        // Process the second API response
        const reviewData = reviewResponse.data
          .map(item => ({
            ...item,
            incidentDate: moment(item.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY'),
            actualImpact: `${item.actualImpact} ${item.potentialImpact ? `(${extractImpactLevel(item.actualImpact)})` : ''}`
          }))
          .filter(i => i.status === 'Reported');

        const reporterData = reporterResponse.data
          .map(item => ({
            ...item,
            incidentDate: moment(item.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY'),
            actualImpact: `${item.actualImpact} ${item.potentialImpact ? `(${extractImpactLevel(item.actualImpact)})` : ''}`
          }))
          .filter(i => i.status === 'Reported');

        // Combine both datasets into a single state
        const combinedData = [...incidentData.reverse(), ...reviewData.reverse(), ...reporterData.reverse()];
        setAllIncident(combinedData);
        setAllIncidentFilter(combinedData);
      }
    } catch (error) {
      console.error("Error fetching incident data:", error);
    }
  };




  // const getIncidentData = async () => {

  //   const params = {
  //     "include": [{ "relation": "user" }, { "relation": "incidentOwner" }, { "relation": "investigator" }, { "relation": "reviewer" }]

  //   };
  //   const response = await API.get(`${ALL_INCIDENT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);


  //   if (response.status === 200) {

  //     const filter = response.data.map((item) => ({
  //       ...item,
  //       incidentDate: moment(item.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY')
  //     }))
  //     setAllIncident([...filter].reverse())
  //     setAllIncidentFilter([...filter].reverse())
  //   }

  // }

  const getIncidentReview = async () => {
    const params = {
      "include": [{ "relation": "user" }, {"relation": "reviewer"}]

    };
    const response = await API.get(`${REPORT_INCIDENT_REVIEW_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);

    if (response.status === 200) {

      const preprocessedData = response.data
        .filter(item => item.status === 'Reported') // Filter for 'Reported' status at source
        .map(item => ({
          ...item,


          incidentDate: moment(item.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY'),
          actualImpact: `${item.actualImpact} ${item.potentialImpact && `(${extractImpactLevel(item.actualImpact)})`}`

        }));

      setIncidentReview([...preprocessedData].reverse())
      setIncidentReviewFilter([...preprocessedData].reverse())
    }
  }

  const getIncidentInves = async () => {

    const params = {
      "include": [{ "relation": "investigator" }, { "relation": "incidentOwner" }, { "relation": "user" }, { "relation": "reviewer" }]

    };


    const response = await API.get(`${REPORT_INCIDENT_INVESTIGATE_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    if (response.status === 200) {

      const preprocessedData = response.data.map(item => ({
        ...item,


        incidentDate: moment(item.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY'),
        actualImpact: `${item.actualImpact} ${item.potentialImpact && `(${extractImpactLevel(item.actualImpact)})`}`

      })).filter(i => i.status !== 'Investigation Completed');
      setIncidentInves([...preprocessedData].reverse())
      setIncidentInvesFilter([...preprocessedData].reverse())
    }
  }

  const getIncidentToBeData = async () => {
    const params = {
      "include": [{ "relation": "user" }]

    };
    const response = await API.get(`${REPORT_INCIDENT_LEAD_INVESTIGATOR_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    if (response.status === 200) {

      const preprocessedData = response.data.map(item => ({
        ...item,


        incidentDate: moment(item.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY'),
        actualImpact: `${item.actualImpact} ${item.potentialImpact && `(${extractImpactLevel(item.actualImpact)})`}`

      })).filter(i => i.status !== 'Investigation Completed');
      setIncidentInvesToBe([...preprocessedData].reverse())
      setIncidentInvesToBeFilter([...preprocessedData].reverse())
    }
  }

  useEffect(() => {

    getActionData()
  }, [
    rendered
  ])


  const getFilteredActions = (applicationType, statusList, data) => {
    return data.filter(action =>
      action.application === applicationType && statusList.includes(action.status)
    );
  }
  return (

    <CardOverlay>
      <AppSwitch value={{ label: 'Incident', value: 'incident' }} />
      <div className='row'>
        <div className='col-12 mb-4'>
          <div className='d-flex align-items-center'>
            <div className='col-1 d-flex'>
              {!applyFilter ?
                <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={showFilter ? {} : { background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                  <div className='d-flex flex-column align-items-end'>
                    <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                    <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                  </div>
                  <i className='pi pi-filter ms-2' style={{ fontSize: 22 }} />
                </div>
                :
                <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={{ background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                  <div className='d-flex flex-column align-items-end'>
                    <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                    <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                  </div>
                  <i className='pi pi-filter-slash ms-2' style={{ fontSize: 22 }} />

                </div>
              }

            </div>
            <div className='col-9 d-flex'>
              {applyFilter && <>
                {filter.length !== 0 &&
                  <h5><b>Location : </b>{filter.map((location, index) => (
                    location !== '' &&
                    <React.Fragment key={index}>
                      <span className='loc-box'>{location}</span>
                      {index < filter.length - 1 && <i className="pi pi-chevron-right me-1 ms-1"></i>}
                    </React.Fragment>
                  ))}</h5>
                }
                {dates.length !== 0 &&
                  <h5 className='ms-3'><b>Month Range :</b> {dates.map((location, index) => (
                    <React.Fragment key={index}>
                      <span className='loc-box'>{location}</span>
                      {index < dates.length - 1 && " To "}
                    </React.Fragment>
                  ))}</h5>
                }
              </>}
            </div>
            <div className='col-2'>
              <div className="p-input-icon-left ">
                <i className="fa fa-search" />
                <InputText type="search" style={{ borderRadius: 8 }} placeholder='Search' onChange={(e) => setSearch(e.target.value)} />
              </div>
            </div>
          </div>
        </div>
        <div className={'col-3'} style={{ paddingRight: 0 }} hidden={showFilter}>

          <AllFilterLocationVertical handleFilter={handleFilter} disableAll={false} period={true} onApplyFilter={onApplyFilter} onCancelFilter={onCancelFilter} />
        </div>
        <div className={!showFilter ? 'col-9' : 'col-12'}>
          <Box sx={{ width: '100%' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={topLevelValue} onChange={handleTopLevelChange} aria-label="observation report table">
                <Tab label={
                  <Typography variant="body1" style={customFontStyle}>
                    My Actions <span className='headerCount'>{combinedData.length}</span>
                  </Typography>
                } value={TOP_LEVEL_TABS.ACTION} />

                <Tab label={
                  <Typography variant="body1" style={customFontStyle}>
                    All Incidents <span className='headerCount'>{allIncident.length}</span>
                  </Typography>
                } value={TOP_LEVEL_TABS.ALL} />

                {isIncidentReview && <Tab label={
                  <Typography variant="body1" style={customFontStyle}>
                    Under Review <span className='headerCount'>{incidentReview.length}</span>
                  </Typography>
                } value={TOP_LEVEL_TABS.REVIEW} />}

                {isIncidentUnderInvestigation && <Tab label={
                  <Typography variant="body1" style={customFontStyle}>
                    Under Investigation <span className='headerCount'>{incidentInves.length}</span>
                  </Typography>
                } value={TOP_LEVEL_TABS.UNDER_INVESTIGATION} />}

                {isIncidentInvestigate && <Tab label={<Typography variant="body1" style={customFontStyle}>
                  To be Investigated by You <span className='headerCount'>{incidentInvesToBe.length}</span>
                </Typography>} value={TABS.INVESTIGATION} />}

              </Tabs>
            </Box>
            <CustomTabPanel value={topLevelValue} tabValue={TOP_LEVEL_TABS.ACTION}>
              <Box sx={{ width: '100%' }}>

                {/* <IncidentInvestigationCard />
                {isIncidentReview && <IncidentCardReport />} */}
                {/* {getFilteredActions('INCIDENT', ['open']).map(action => ( */}
                <ActionCard action={combinedData} applicationType="INCIDENT" setRendered={setRendered} />
                {/* ))} */}

              </Box>
            </CustomTabPanel>

            <CustomTabPanel value={topLevelValue} tabValue={TOP_LEVEL_TABS.ALL}>
              <AllIncidentReport incident={allIncident} getIncidentData={getCombinedIncidentData} setRendered={setRendered} />
            </CustomTabPanel>

            {isIncidentReview && <CustomTabPanel value={topLevelValue} tabValue={TOP_LEVEL_TABS.REVIEW} >
              <IncidentReview incident={incidentReview} getIncidentData={getIncidentReview} setRendered={setRendered} />
            </CustomTabPanel>}
            {isIncidentUnderInvestigation && <CustomTabPanel value={topLevelValue} tabValue={TOP_LEVEL_TABS.UNDER_INVESTIGATION}>
              <UnderIncidentInvestigation incident={incidentInves.filter(i => i.status !== 'Investigation Completed')} getIncidentData={getIncidentInves} setRendered={setRendered} />
            </CustomTabPanel>}

            {isIncidentInvestigate && <CustomTabPanel value={topLevelValue} tabValue={TABS.INVESTIGATION}>
              <IncidentInvestigation incident={incidentInvesToBe.filter(i => i.status !== 'Investigation Completed')} getIncidentData={getIncidentToBeData} setRendered={setRendered} />
            </CustomTabPanel>}

            {/* <CustomTabPanel value={topLevelValue} tabValue={TOP_LEVEL_TABS.LIST}>
          <Box sx={{ width: '100%' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>


              <h4 className='mt-5'>Incident Reports</h4>

              <Tabs value={value} onChange={handleChange} aria-label="incident report table">
                <Tab label="All" value={TABS.ALL} />
                <Tab label="Reported by You" {...a11yProps(1)} />
                {isIncidentReview && <Tab label="Pending Review" value={TABS.REVIEW} />}
                {isIncidentTrigger && <Tab label="To be Triggered By You" {...a11yProps(3)} />}
                {isIncidentInvestigate && <Tab label="To be Investigated by You" value={TABS.INVESTIGATION} />}
                {isIncidentUnderInvestigation && <Tab label="Under Investigation" value={TABS.UNDER_INVESTIGATION} />}

              </Tabs>
            </Box>


            <CustomTabPanel value={value} tabValue={TABS.ALL}>
              <AllIncidentReport />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
              <IncidentReport />
            </CustomTabPanel>
            {isIncidentReview && <CustomTabPanel value={value} tabValue={TABS.REVIEW}>
              <IncidentReview />
            </CustomTabPanel>}
            {isIncidentTrigger && <CustomTabPanel value={value} index={3}>
              <IncidentTrigger />
            </CustomTabPanel>}
            {isIncidentInvestigate && <CustomTabPanel value={value} tabValue={TABS.INVESTIGATION}>
              <IncidentInvestigation />
            </CustomTabPanel>}

            {isIncidentUnderInvestigation && <CustomTabPanel value={value} tabValue={TABS.UNDER_INVESTIGATION}>
              <UnderIncidentInvestigation />
            </CustomTabPanel>}

          </Box>
        </CustomTabPanel> */}

          </Box>
        </div>
      </div>
    </CardOverlay>
  )

}

export default Incident;
// @ts-nocheck
import React from "react";
import { Dropdown } from 'react-bootstrap';
import { useHistory } from "react-router-dom";

const ListBoxItem = (props) => {
    const history = useHistory();
    const handlePrint = (id) => {
        props.handlePrint(id)
    }


    return (
        <>

            <div className={`list d-flex justify-content-between align-items-center p-3 border-bottom cursor-pointer ${(props.selectedId === props.data.id ? "active" : '')}`} onClick={() => props.onHandleSelect(props.data.id)}>

                <div className="content">
                    <p className="list-name m-0">{props.data.title}</p>
                    {/* <p className="list_text">Hello, last date for registering for the annual music event is closing in</p> */}
                </div>
                <div className="options">
                    <Dropdown variant="p-0">
                        <Dropdown.Toggle variant="dropdown-toggle p-0 no-caret">
                            <i className="ti-more-alt"></i>
                        </Dropdown.Toggle>
                        <Dropdown.Menu>
                            <Dropdown.Item><div onClick={() => props.onHandleConfigure(props.data.id)}><i className="typcn typcn-cog me-2"></i> Configure</div></Dropdown.Item>
                            <Dropdown.Item><div onClick={() => props.onHandleClone(props.data.id)}><i className="mdi mdi-content-copy me-2"></i> Clone</div></Dropdown.Item>
                            <Dropdown.Item><div onClick={() => history.push('/move')}><i className="typcn typcn-export me-2"></i> Move To</div></Dropdown.Item>
                            <Dropdown.Item><div onClick={() => props.onHandleActivity(props.data.id)}><i className="mdi mdi-history me-2"></i> Completed WO</div></Dropdown.Item>
                            <Dropdown.Item><div onClick={() => handlePrint(props.data.id)}><i className="mdi mdi-printer me-2"></i> Print</div></Dropdown.Item>
                            <Dropdown.Item><div onClick={() => props.onHandleDelete(props.data.id)}><i className="mdi mdi-delete me-2"></i> Delete</div></Dropdown.Item>
                        </Dropdown.Menu>
                    </Dropdown>
                </div>
            </div>



        </>
    )
}

export default ListBoxItem;
import React, { useEffect, useState } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { API_URL, INCIDENT_REVIWERER_URL, OBSERVATION_REPORT_URL, REPORT_INCIDENT_URL, REPORT_INCIDENT_URL_WITH_ID } from '../constants';
import { Modal, Button, Form } from 'react-bootstrap';
import { DropzoneArea } from 'material-ui-dropzone';
import Switch from "react-switch";
import { BodyComponent } from "reactjs-human-body";

import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';

import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import axios from 'axios';
import DynamicLocation from './DynamicLocation';
import LocationDropdown from './LocationDropdown';
import RiskCategoryDropdown from './RiskCategoryDropdown';
import WorkActivityDropdown from './WorkActivityDropdown';
import SurfaceTypeDropdown from './SurfaceTypeDropdown';
import SurfaceConditionDropdown from './SurfaceConditionDropdown';
import IncidentCircumstanceDropdown from './IncidentCircumstanceDropdown';
import FilterLocation from './FilterLocation';
import AllFilterLocation from './AllLocationFilter';
import moment from 'moment';

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const IncidentReport = () => {
  const history = useHistory();

  const defaultMaterialTheme = createTheme();
  const tableOptions = {
    actionsColumnIndex: -1,
    actionsCellStyle: {
      padding: '1.125rem 1.375rem',
    },
    pageSize: 20,
    headerStyle: {

      padding: '1.125rem 1.375rem',
      fontSize: '0.812rem'
    },
    rowStyle: {
      // padding: '1.125rem 1.375rem',
      fontSize: '0.812rem'
    }
  }
  const incidentColumns = [
    {
      title: "Incident Date",
      defaultSort: 'desc',
      field: "incidentDate",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      },
      render: rowData => {
        return `${moment(rowData.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY hh:mm A')}`
      }
    },
    {
      title: "Description",
      field: "description",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    },
    {
      title: "Incident Category",
      field: "IncidentCategory",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    },
    {
      title: "Impact Classification",
      field: "actualImpact",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    }

  ]

  const tableActions = [
    {
      icon: 'visibility',
      tooltip: 'View',
      onClick: (event, rowData) => {
        // Do save operation
        // console.log(rowData)
        viewIncident(rowData.id)
      }
    }
  ]

  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };

  const [data, setData] = useState([]);
  const [filterData, setFilterData] = useState([]);
  useEffect(() => {
    getIncidentData();
  }, [])

  const getIncidentData = async () => {
    const response = await API.get(REPORT_INCIDENT_URL);
    if (response.status === 200) {
      setData(response.data.filter(i => !i.reviewerId))
      setFilterData(response.data.filter(i => !i.reviewerId))
    }
  }
  const [files, setFiles] = useState([]);

  const handleFileChange = (file) => {
    setFiles(file)

  }


  const [showModal, setShowModal] = useState(false)

  const [injury, setInjury] = useState(false)
  const [firstAid, setFirstAid] = useState(false)
  const [personInjured, setPersonInjured] = useState(false);
  const [medicalTreatment, setMedicalTreatment] = useState(false);
  const [medicalLeave, setMedicalLeave] = useState(false);
  const [option8, setOption8] = useState("");
  const [option7, setOption7] = useState("");
  const [option6, setOption6] = useState("");
  const [option5, setOption5] = useState("");
  const [option4, setOption4] = useState("");
  const [option3, setOption3] = useState("");
  const [option2, setOption2] = useState("");
  const [option1, setOption1] = useState("");

  const [status, setStatus] = useState("");
  const [remarks, setRemarks] = useState("");

  useEffect(() => {

    const stringArray = [
      "fallofperson>2m/6ft",
      "fallofmaterials>2m/6ft-craneload",
      "fallofmaterials>2m/6ft-fixing/fixturefailure",
      "fallofmaterials>2m/6ft-scaffold",
      "fallofmaterials>2m/6ft-truckorvehicleload"
    ];
    if (option1 && option2 && option3 && option4 && option5 && option6 && incidentData) {


      if (option2 === "No. But, it could have" || option3 === "No. But, it could have" || stringArray.some(element => element.includes(incidentData.incidentCircumstanceType.name.replace(/\s+/g, '').toLowerCase()))) {
        // setRemarks('Potentially Serious Incident')
      }

      if (option6 === "Yes") {
        setStatus("Level 1");


      }
      if (option5 === "Yes") {
        setStatus("Level 2");

      }
      if (option4 === "Yes") {
        setStatus("Level 3");


      }

      if (option3 === "Yes" || option3 === "No. But, it could have") {
        setStatus("Level 4");
        if (option3 === "Yes" && !stringArray.some(element => element.includes(incidentData.incidentCircumstanceType.name.replace(/\s+/g, '').toLowerCase()))) {
          setRemarks('')
        }
      }

      if (option2 === "Yes") {
        setStatus("Level 5");
        if (option2 === "Yes" && !stringArray.some(element => element.includes(incidentData.incidentCircumstanceType.name.replace(/\s+/g, '').toLowerCase()))) {
          setRemarks('')
        }
      }

      if (option2 === "No. But, it could have") {
        setStatus("Level 4");
      }
      if (option1 === "Yes" && option2 !== "Yes") {
        setStatus("Level 4");
      }

      if (option3 === "No" && option2 === "No" && option4 === "No" && !stringArray.some(element => element.includes(incidentData.incidentCircumstanceType.name.replace(/\s+/g, '').toLowerCase()))) {
        setRemarks('')
      }
      if (
        option6 === "No" &&
        option5 === "No" &&
        option4 === "No" &&
        option3 === "No" &&
        option2 === "No" &&
        option1 === "No" &&
        !stringArray.some(element => element.includes(incidentData.incidentCircumstanceType.name.replace(/\s+/g, '').toLowerCase()))
      ) {
        setStatus("Near Miss");

        setRemarks('');

      }
    }
  }, [option6, option5, option4, option3, option2, option1, incidentData]);

  const [currentIncident, setCurrentIncident] = useState('')
  const viewIncident = async (id) => {
    setCurrentIncident(id);
    getReportIncident(id);
    setShowModal(true)
  }

  const [incidentData, setIncidentData] = useState({})
  const getReportIncident = async (id) => {

    const uriString = { include: ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationFive', 'locationSix', 'incidentCircumstanceCategory', 'incidentCircumstanceDescription', 'incidentCircumstanceType', 'lighting', 'riskCategory', 'surfaceCondition', 'surfaceType', 'workActivity'] }

    const url = `${REPORT_INCIDENT_URL_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

    const response = await API.get(url);
    if (response.status === 200) {
      console.log(response.data)
      const data = response.data;
      setIncidentData(data)
      setStatus(data.actualImpact)
      setOption1(data.dangerousOccurance)
      setOption2(data.fatality)
      setOption3(data.injury)
      setOption4(data.lostTime)
      setOption5(data.medicalTreatment)
      setOption6(data.firstAid)
      setOption7(data.lossOfConscious)

    }
  }

  const [InformationData, setInformationData] = useState({
    whenIncidentHappen: "",
    files: [],
    personInvolved: "",
    witness: "",
    howIncidentHappen: "",
    equipment: "",
    activitiesPerformed: "",
    unsualActivity: "",
    safeProcedure: "",
    administeredBy: "",
    medicalFaciltiyName: "",
    driverName: "",
    treatmentDescription: "",
    medicalLeaveDays: 0,
    regularWorkDate: "",
    modifiedWorkDate: "",
    regularWork: false,
    modifiedWork: false,
    ambulance: false,
    companyVehicle: false,
    privateVehicle: false,
    body: {
      head: {
        show: true,
        selected: false
      },
      left_shoulder: {
        show: true,
        selected: false
      },
      right_shoulder: {
        show: true,
        selected: false
      },
      left_arm: {
        show: true,
        selected: false
      },
      right_arm: {
        show: true,
        selected: false
      },
      chest: {
        show: true,
        selected: false
      },
      stomach: {
        show: true,
        selected: false
      },
      left_leg: {
        show: true,
        selected: false
      },
      right_leg: {
        show: true,
        selected: false
      },
      left_hand: {
        show: true,
        selected: false
      },
      right_hand: {
        show: true,
        selected: false
      },
      left_foot: {
        show: true,
        selected: false
      },
      right_foot: {
        show: true,
        selected: false
      }
    }

  })

  const [riskControl, setRiskControl] = useState({
    immediateActions: [
      {
        date: new Date(),
        description: ""
      }
    ],
    controlMeasures: [
      {
        completionDate: new Date(),
        personResponsible: "",
        controlMeasures: ""
      }
    ],
    riskAssessment: [
      {
        name: "",
        completionDate: new Date(),
        personResponsible: ""
      }
    ]
  })

  const addImmediateAction = () => {
    setRiskControl(prevState => ({
      ...prevState,
      immediateActions: [...prevState.immediateActions, { date: new Date(), description: "" }],
    }));
  };

  const handleImmediateActionChange = (index, field, value) => {
    setRiskControl(prevState => {
      const updatedActions = [...prevState.immediateActions];
      updatedActions[index][field] = value;
      return { ...prevState, immediateActions: updatedActions };
    });
  };

  const addcontrolMeasures = () => {
    setRiskControl(prevState => ({
      ...prevState,
      controlMeasures: [...prevState.controlMeasures, {
        completionDate: new Date(),
        personResponsible: "",
        controlMeasures: ""
      }],
    }));
  };

  const handlecontrolMeasuresChange = (index, field, value) => {
    setRiskControl(prevState => {
      const updatedActions = [...prevState.controlMeasures];
      updatedActions[index][field] = value;
      return { ...prevState, controlMeasures: updatedActions };
    });
  };

  const addRiskAssessment = () => {
    setRiskControl(prevState => ({
      ...prevState,
      riskAssessment: [...prevState.riskAssessment, {
        name: "",
        completionDate: new Date(),
        personResponsible: ""
      }],
    }));
  };

  const handleRiskAssessmentChange = (index, field, value) => {
    setRiskControl(prevState => {
      const updatedActions = [...prevState.riskAssessment];
      updatedActions[index][field] = value;
      return { ...prevState, riskAssessment: updatedActions };
    });
  };


  const handleDeleteImmediateAction = (index) => {
    const newImmediateActions = [...riskControl.immediateActions];
    newImmediateActions.splice(index, 1);
    setRiskControl(prevState => ({ ...prevState, immediateActions: newImmediateActions }));
  };

  const handleDeleteControlMeasure = (index) => {
    const newControlMeasures = [...riskControl.controlMeasures];
    newControlMeasures.splice(index, 1);
    setRiskControl(prevState => ({ ...prevState, controlMeasures: newControlMeasures }));
  };

  const handleDeleteRiskAssessment = (index) => {
    const newRiskAssessments = [...riskControl.riskAssessment];
    newRiskAssessments.splice(index, 1);
    setRiskControl(prevState => ({ ...prevState, riskAssessment: newRiskAssessments }));
  };

  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
    const filteredData = data.filter(item => {
      return (
        (locationOneId === '' || item.locationOneId === locationOneId) &&
        (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
        (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
        (locationFourId === '' || item.locationFourId === locationFourId)
      );
    });

    setFilterData(filteredData);
  };

  const handleWheel = (event) => {

    event.target.blur();  // Prevents scrolling changing the number
  };

  const [locationOneId, setLocationOneId] = useState('')
  const [locationTwoId, setLocationTwoId] = useState('');
  const [locationThreeId, setLocationThreeId] = useState('');
  const [locationFourId, setLocationFourId] = useState('');
  const [locationFiveId, setLocationFiveId] = useState('');
  const [locationSixId, setLocationSixId] = useState('');

  const steps = [

    {
      label: 'Basic Information',
      description: (
        <>
          <DynamicLocation handleFilter={handleFilter} />


          {incidentData.locationOne && (<>

            <LocationDropdown setIncidentData={setIncidentData}
              incidentData={incidentData} readOnly={true} />
            <p>
              <i className='mdi mdi-calendar-month'></i> {incidentData.incidentDate}
            </p>

            <div className='row'>
              <div className='col'>
                <div className='form-group'>
                  <label htmlFor="">Incident Category</label>
                  <input type='text' disabled={true} value={incidentData.IncidentCategory} onChange={(e) => { setIncidentData((prev) => ({ ...prev, IncidentCategory: e.target.value })) }} className='form-control' />
                </div>
              </div>
            </div>

            <div className='row'>
              <div className='col'>
                <div className='form-group'>
                  <label htmlFor="">Incident Description</label>
                  <input type='text' value={incidentData.description}
                    disabled={true}
                    onChange={(e) => { setIncidentData((prev) => ({ ...prev, description: e.target.value })) }} className='form-control' />
                </div>
              </div>
            </div>

            {<IncidentCircumstanceDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={true} />}

            {<SurfaceTypeDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={true} />}
            {<SurfaceConditionDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={true} />}

            {<WorkActivityDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={true} />}




            {<RiskCategoryDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={true} />}

          </>)}
        </>
      )
    },
    {
      label: 'Re-assessment',
      description: (
        <>
          <div className='row'>
            <div className='col'>
              <div className='form-group'>
                <p>Was this as a result of a Dangerous Occurrence</p>
                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options7" id="option18" checked={option1 === "Yes"} onChange={(e) => setOption1('Yes')} autocomplete="off" /> Yes
                  </label>
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options7" id="option19" checked={option1 === "No"} onChange={(e) => setOption1('No')} autocomplete="off" /> No
                  </label>
                </div>
              </div>

            </div>
          </div>
          <div className='row full-width-btn-group'>
            <h5>Did this incident result in</h5>
            <div className='col-md-6'>

              <div className='form-group'>
                <p>A Fatality?</p>
                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options1" id="option1" checked={option2 === "Yes"} onChange={(e) => setOption2('Yes')} autocomplete="off" /> Yes
                  </label>
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options1" id="option2" checked={option2 === "No. But, it could have"} onChange={(e) => setOption2('No. But, it could have')} autocomplete="off" /> No. But, it could have
                  </label>
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options1" id="option3" checked={option2 === "No"} onChange={(e) => setOption2('No')} autocomplete="off" /> No
                  </label>
                </div>
              </div>
            </div>
            <div className='col-md-6'>
              <div className='form-group'>
                <p>An Injury or Occupational illness resulting in permanent disability?</p>
                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options2" id="option4" checked={option3 === "Yes"} onChange={(e) => setOption3('Yes')} autocomplete="off" /> Yes
                  </label>
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options2" id="option5" checked={option3 === "No. But, it could have"} onChange={(e) => setOption3('No. But, it could have')} autocomplete="off" /> No. But, it could have
                  </label>
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options2" id="option6" checked={option3 === "No"} onChange={(e) => setOption3('No')} autocomplete="off" /> No
                  </label>
                </div>
              </div>
            </div>

            <div className='col-md-4'>
              <div className='form-group'>
                <p>Lost Time Incident (LTI)?</p>
                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options3" id="option7" checked={option4 === "Yes"} onChange={(e) => setOption4('Yes')} autocomplete="off" /> Yes
                  </label>

                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options3" id="option9" checked={option4 === "No"} onChange={(e) => setOption4('No')} autocomplete="off" /> No
                  </label>
                </div>
              </div>
            </div>
            <div className='col-md-4'>
              <div className='form-group'>
                <p>Medical Treatment of illness / injury?</p>
                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options4" id="option10" checked={option5 === "Yes"} onChange={(e) => setOption5('Yes')} autocomplete="off" /> Yes
                  </label>

                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options4" id="option12" checked={option5 === "No"} onChange={(e) => setOption5('No')} autocomplete="off" /> No
                  </label>
                </div>
              </div>
            </div>
            <div className='col-md-4'>
              <div className='form-group'>
                <p>Need to administer First Aid?</p>
                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options5" id="option13" checked={option6 === "Yes"} onChange={(e) => setOption6('Yes')} autocomplete="off" /> Yes
                  </label>

                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options5" id="option15" checked={option6 === "No"} onChange={(e) => setOption6('No')} autocomplete="off" /> No
                  </label>
                </div>
              </div>
            </div>
            <div className='col-md-4'>
              <div className='form-group'>
                <p>loss of consciousness?</p>
                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options6" id="option16" checked={option7 === "Yes"} onChange={(e) => setOption7('Yes')} autocomplete="off" /> Yes
                  </label>
                  <label className="btn btn-light">
                    <input type="radio" disabled={true} name="options6" id="option17" checked={option7 === "No"} onChange={(e) => setOption7('No')} autocomplete="off" /> No
                  </label>
                </div>
              </div>
            </div>

            <div role="alert" class="fade alert alert-danger show">{status} </div>
            <h4 className='text-center'>{remarks && `${remarks}`}</h4>
          </div>

        </>
      ),
    },
    {
      label: 'Information gathering',
      description:
        (<>
          <div className='row'>
            <div className='col'>
              <div className='form-group'>
                <label htmlFor="">Where and when did the incident happen?</label>
                <input type='text' disabled={true} onChange={(e) => setInformationData(prev => ({ ...prev, whenIncidentHappen: e.target.value }))} value={InformationData.whenIncidentHappen} className='form-control' />
              </div>
            </div>
          </div>

          <div className='row'>
            <div className='col'>
              <div className='form-group'>
                <label htmlFor="">Who was injured/suffered ill health or was otherwise involved in the incident?</label>
                <input type='text' disabled={true} onChange={(e) => setInformationData(prev => ({ ...prev, personInvolved: e.target.value }))} value={InformationData.personInvolved} className='form-control' />
              </div>
            </div>
          </div>
          <div className='row'>
            <div className='col'>
              <div className='form-group'>
                <label htmlFor="">Who witnessed the incident?</label>
                <input type='text' disabled={true} onChange={(e) => setInformationData(prev => ({ ...prev, witness: e.target.value }))} value={InformationData.witness} className='form-control' />
              </div>
            </div>
          </div>
          <div className='row'>
            <div className='col'>
              <div className='form-group'>
                <label htmlFor="">How did the incident happen?</label>
                <input type='text' disabled={true} onChange={(e) => setInformationData(prev => ({ ...prev, howIncidentHappen: e.target.value }))} value={InformationData.howIncidentHappen} className='form-control' />
              </div>
            </div>
          </div>
          <div className='row'>
            <div className='col'>
              <div className='form-group'>
                <label htmlFor="">What equipment was involved?</label>
                <input type='text' disabled={true} onChange={(e) => setInformationData(prev => ({ ...prev, equipment: e.target.value }))} value={InformationData.equipment} className='form-control' />
              </div>
            </div>
          </div>

          <div className='row'>
            <div className='col'>
              <div className='form-group'>
                <label htmlFor="">What activities were being performed at the time of the incident?</label>
                <input type='text' disabled={true} onChange={(e) => setInformationData(prev => ({ ...prev, activitiesPerformed: e.target.value }))} value={InformationData.activitiesPerformed} className='form-control' />
              </div>
            </div>
          </div>

          <div className='row'>
            <div className='col'>
              <div className='form-group'>
                <label htmlFor="">Was there anything unusual or different about the working conditions?</label>
                <input type='text' disabled={true} onChange={(e) => setInformationData(prev => ({ ...prev, unsualActivity: e.target.value }))} value={InformationData.unsualActivity} className='form-control' />
              </div>
            </div>
          </div>

          <div className='row'>
            <div className='col'>
              <div className='form-group'>
                <label htmlFor="">Were there adequate safe working procedures and were they followed?</label>
                <input type='text' disabled={true} onChange={(e) => setInformationData(prev => ({ ...prev, safeProcedure: e.target.value }))} value={InformationData.safeProcedure} className='form-control' />
              </div>
            </div>
          </div>

          <div className='row'>
            <div className='col'>
              <div className='form-group d-flex align-items-center'>
                <label htmlFor="" disabled={true} className='m-0 me-3'>Were injuries sustained?</label>

                <Switch disabled={true} onChange={(value) => setInjury(value)} checked={injury} />
              </div>
            </div>
          </div>

          {
            injury && <>
              <div className='row'>
                <h4>Parts of Body Injured</h4>
                <p>Please indicate the location of all injuries and describe the types of injuries.</p>
                <div className='col'>
                  <BodyComponent
                    partsInput={InformationData.body}
                    onChange={(value) => setInformationData(prev => ({ ...prev, value }))}
                  />
                </div>
                <div className='col'>

                </div>
              </div>
              <div className='row'>
                <div className='col'>
                  <div className='form-group d-flex align-items-center'>
                    <label htmlFor="" className='m-0 me-3'>Was first aid administered on site?</label>

                    <Switch disabled={true} onChange={(value) => setFirstAid(value)} checked={firstAid} />
                  </div>
                </div>
              </div>
              {
                firstAid && <div className='row'>
                  <div className='col'>

                    <div className='form-group'>
                      <label htmlFor="">Administered By</label>
                      <input disabled={true} type='text' onChange={(e) => setInformationData(prev => ({ ...prev, administeredBy: e.target.value }))} value={InformationData.administeredBy} className='form-control' />
                    </div>

                  </div>
                </div>
              }

              <div className='row'>
                <div className='col'>
                  <div className='form-group d-flex align-items-center'>
                    <label htmlFor="" className='m-0 me-3'>Was Injured Person taken to an offsite
                      medical facility?</label>

                    <Switch disabled={true} onChange={(value) => setPersonInjured(value)} checked={personInjured} />
                  </div>
                </div>
              </div>
              {
                personInjured && <div className='row'>
                  <div className='col'>

                    <div className='form-group'>
                      <label htmlFor="">Medical Facility Name</label>
                      <input type='text' disabled={true} onChange={(e) => setInformationData(prev => ({ ...prev, medicalFaciltiyName: e.target.value }))} value={InformationData.medicalFaciltiyName} className='form-control' />
                    </div>

                  </div>
                </div>
              }

              {/* checkbox mode of transport */}

              <div className='row'>
                <div className='col'>
                  <div className='form-group'>
                    <label htmlFor="">Mode of Transport:</label><br />
                    <input type='checkbox' disabled={true} className='ms-2' checked={InformationData.ambulance} onChange={(e) => setInformationData(prev => ({ ...prev, ambulance: e.target.checked }))} /> Ambulance
                    <input type='checkbox' disabled={true} className='ms-2' checked={InformationData.companyVehicle} onChange={(e) => setInformationData(prev => ({ ...prev, companyVehicle: e.target.checked }))} /> Company Vehicle
                    <input type='checkbox' disabled={true} checked={InformationData.privateVehicle} onChange={(e) => setInformationData(prev => ({ ...prev, privateVehicle: e.target.checked }))} className='ms-2' /> Private Vehicle
                    <input type='text' disabled={true} className='form-control ms-2' placeholder='Name/Role of Driver' />
                  </div>
                </div>
              </div>

              <div className='row'>
                <div className='col'>
                  <div className='form-group d-flex align-items-center'>
                    <label htmlFor="" className='m-0 me-3'>Was medical treatment received?</label>

                    <Switch disabled={true} onChange={(value) => setMedicalTreatment(value)} checked={medicalTreatment} />
                  </div>
                </div>
              </div>
              {
                medicalTreatment && <div className='row'>
                  <div className='col'>

                    <div className='form-group'>
                      <label htmlFor="">Brief Description of the treatment</label>
                      <input disabled={true} type='text' onChange={(e) => setInformationData(prev => ({ ...prev, treatmentDescription: e.target.value }))} value={InformationData.treatmentDescription} className='form-control' />
                    </div>

                  </div>
                </div>
              }

              <div className='row'>
                <div className='col'>
                  <div className='form-group d-flex align-items-center'>
                    <label htmlFor="" className='m-0 me-3'>Is medical leave given?</label>

                    <Switch disabled={true} onChange={(value) => setMedicalLeave(value)} checked={medicalLeave} />
                  </div>
                </div>
              </div>
              {
                medicalLeave && <div className='row'>
                  <div className='col-3'>

                    <div className='form-group'>
                      <label htmlFor="">Days</label>
                      <input disabled={true} type='number' onWheel={handleWheel} onChange={(e) => setInformationData(prev => ({ ...prev, medicalLeaveDays: e.target.value }))} value={InformationData.medicalLeaveDays} min="1" className='form-control' />
                    </div>

                  </div>
                </div>
              }
              <div className='row'>
                <div className='col'>
                  <div className='form-group'>
                    <label htmlFor="">Injured Person's Return-to-Work info:</label> <br />
                    <input disabled={true} type='checkbox' checked={InformationData.regularWork} onChange={(e) => setInformationData(prev => ({ ...prev, regularWork: e.target.checked }))} className='ms-2' /> Regular Work <br />
                    <input disabled={true} type='date' className='form-control ms-2' /><br /><br />
                    <input disabled={true} type='checkbox' checked={InformationData.modifiedWork} onChange={(e) => setInformationData(prev => ({ ...prev, modifiedWork: e.target.checked }))} className='ms-2' /> Modified Work<br />

                    <input disabled={true} type='date' className='form-control ms-2' />
                  </div>
                </div>
              </div>
            </>
          }
        </>)
    },
    {
      label: 'Risk Control Action Plan',
      description: (<>
        <p className="h5 mb-4">Immediate Actions Taken (Post Damage / Incident)?</p>
        {riskControl.immediateActions.map((action, index) => (
          <div className="form-group d-flex align-items-center" key={index}>
            <label>
              Date


              <input
                disabled={true}
                className="form-control"
                type="text"
                value={moment(action.date, 'YYYY-MM-DD').format('Do MMM YYYY')}

              />
            </label>

            <label>
              Description
              <input
                disabled={true}
                className="form-control"
                type="text"
                value={action.description}
                onChange={(e) =>
                  handleImmediateActionChange(index, "description", e.target.value)
                }
              />
            </label>

          </div>
        ))}
        <button variant="light" className='btn btn-light mb-4' type="button" onClick={addImmediateAction}>
          Add More
        </button>

        <p className="h5 mb-4">Which control measures should be implemented in the short and long term?</p>
        {riskControl.controlMeasures.map((action, index) => (
          <div className="form-group d-flex align-items-center" key={index}>
            <label>
              Corrective/Control measures:
              <input
                disabled={true}
                className="form-control"
                type="text"
                value={action.controlMeasures}
                onChange={(e) =>
                  handlecontrolMeasuresChange(index, "controlMeasures", e.target.value)
                }
              />
            </label>

            <label>
              Due Date
              <input
                disabled={true}
                className="form-control"
                type="text"
                value={moment(action.completionDate, 'YYYY-MM-DD').format('Do MMM YYYY')}
                onChange={(e) =>
                  handlecontrolMeasuresChange(index, "completionDate", e.target.value)
                }
              />
            </label>

            <label>
              Person Responsible
              <input
                disabled={true}
                className="form-control"
                type="text"
                value={action.personResponsible}
                onChange={(e) =>
                  handlecontrolMeasuresChange(index, "personResponsible", e.target.value)
                }
              />
            </label>

          </div>
        ))}
        <button variant="light" className='btn btn-light mb-4' type="button" onClick={addcontrolMeasures}>
          Add More
        </button>

        <p className="h5 mb-4">Which risk assessments and safe working procedures need to be reviewed and updated?</p>
        {riskControl.riskAssessment.map((action, index) => (
          <div className="form-group d-flex align-items-center" key={index}>
            <label>
              Name of risk assessment / safe working procedure: reporting and
              refined in investigation if needed
              <input
                disabled={true}
                className="form-control"
                type="text"
                value={action.name}
                onChange={(e) =>
                  handleRiskAssessmentChange(index, "name", e.target.value)
                }
              />
            </label>

            <label>
              Due Date
              <input
                disabled={true}
                className="form-control"
                type="date"
                value={moment(action.completionDate, 'YYYY-MM-DD').format('Do MMM YYYY')}
                onChange={(e) =>
                  handleRiskAssessmentChange(index, "completionDate", e.target.value)
                }
              />
            </label>

            <label>
              Person Responsible
              <input
                disabled={true}
                className="form-control"
                type="text"
                value={action.personResponsible}
                onChange={(e) =>
                  handleRiskAssessmentChange(index, "personResponsible", e.target.value)
                }
              />
            </label>

          </div>
        ))}
        <button variant="light" className='btn btn-light mb-4' type="button" onClick={addRiskAssessment}>
          Add More
        </button>



      </>),
    },
  ];

  const [activeStep, setActiveStep] = React.useState(0);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  const [selectedReviewer, setSelectedReviewer] = useState('');
  const handleSubmit = async () => {
    const response = await API.patch(REPORT_INCIDENT_URL_WITH_ID(currentIncident), {
      actualImpact: status,
      dangerousOccurance: option1,
      fatality: option2,
      injury: option3,
      lostTime: option4,
      medicalTreatment: option5,
      firstAid: option6,
      lossOfConscious: option7,
      informationStep: InformationData,
      riskControl: riskControl,
      reviewerId: selectedReviewer,
      locationOneId: incidentData.locationOneId,
      locationTwoId: incidentData.locationTwoId,
      locationThreeId: incidentData.locationThreeId,
      locationFourId: incidentData.locationFourId,
      locationFiveId: incidentData.locationFiveId,
      locationSixId: incidentData.locationSixId,
      description: incidentData.description,
      IncidentCategory: incidentData.IncidentCategory,
      incidentCircumstanceCategoryId: incidentData.incidentCircumstanceCategoryId,
      incidentCircumstanceDescriptionId: incidentData.incidentCircumstanceDescriptionId,
      incidentCircumstanceTypeId: incidentData.incidentCircumstanceTypeId,
      surfaceTypeId: incidentData.surfaceTypeId,
      surfaceConditionId: incidentData.surfaceConditionId,
      workActivityId: incidentData.workActivityId,
      riskCategoryId: incidentData.riskCategoryId

    })

    if (response.status === 204) {

      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      getIncidentData();
    }
  }

  const [incidentReviewer, setIncidentReviewer] = useState([])

  useEffect(() => {
    const fetchData = async () => {
      try {

        const result = await axios.post(INCIDENT_REVIWERER_URL, { locationOneId: incidentData.locationOneId, locationTwoId: incidentData.locationTwoId, locationThreeId: incidentData.locationThreeId, locationFourId: incidentData.locationFourId });
        setIncidentReviewer(result.data);
      } catch (error) {
        console.error('Error fetching data', error);
      }
    };
    if (incidentData.locationOneId && incidentData.locationTwoId && incidentData.locationThreeId && incidentData.locationFourId)
      fetchData();

  }, [incidentData.locationFourId])

  return (
    <>

      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body">

                <h4 className="card-title">Reported Incident</h4>
                <div className="row">
                  <div className="col-12">
                    <div>
                      {/* <button type="button" className="btn btn-light btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setMdShow(true); }}><i className="mdi mdi-account-plus mr-2" /> Create New User</button> */}
                      <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} />
                      <ThemeProvider theme={defaultMaterialTheme}>
                        <MaterialTable
                          columns={incidentColumns}
                          data={filterData}
                          title="Incident Reports"
                          style={tableStyle}
                          actions={tableActions}
                          options={tableOptions}


                        />
                      </ThemeProvider>
                      {/* <DataTables thead={thead} options={options} /> */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Modal
        show={showModal}
        size="lg"
        onHide={() => setShowModal(false)}
        aria-labelledby="example-modal-sizes-title-md"
        backdrop="static"
      >
        <Modal.Header>
          Information Gathering
        </Modal.Header>

        <Modal.Body>

          <Box>
            <Stepper activeStep={activeStep} orientation="vertical">
              {steps.map((step, index) => (
                <Step key={step.label}>
                  <StepLabel
                    onClick={() => setActiveStep(index)}
                    style={{ cursor: 'pointer' }}
                  >
                    {step.label}
                  </StepLabel>
                  <StepContent>
                    <Typography>{step.description}</Typography>
                    <Box sx={{ mb: 2 }}>
                      <div>
                        <Button
                          disabled={index === 0}
                          className='mt-2'
                          onClick={handleBack}
                          sx={{ mt: 1, mr: 1 }}
                        >
                          Back
                        </Button>

                        {index === steps.length - 1 ? (
                          <>
                            <div className='form-group'>
                              <select onChange={(e) => setSelectedReviewer(e.target.value)} className='form-control'>
                                <option value={''}>Choose Reviewer</option>
                                {
                                  incidentReviewer.map(user => {
                                    return (
                                      <option value={user.id}>{user.firstName}</option>
                                    )
                                  })
                                }
                              </select>
                            </div>
                            <Button
                              variant="light"
                              className='me-2 mt-2'
                              onClick={handleSubmit}
                              sx={{ mt: 1, mr: 1 }}
                            >
                              Share to Reviewer
                            </Button>
                          </>

                        ) : (

                          <Button
                            variant="light"
                            className='me-2 mt-2'
                            onClick={handleNext}
                            sx={{ mt: 1, mr: 1 }}
                          >
                            Continue
                          </Button>
                        )}


                      </div>
                    </Box>
                  </StepContent>
                </Step>
              ))}
            </Stepper>
            {activeStep === steps.length && (
              <Paper square elevation={0} sx={{ p: 3 }}>
                <Typography>Submitted to Reviewer</Typography>

              </Paper>
            )}
          </Box>


        </Modal.Body>

        <Modal.Footer className="flex-wrap">



          <Button variant="light" onClick={() => { setShowModal(false); setActiveStep(0); }}>Close</Button>




        </Modal.Footer>
      </Modal>

    </>
  )
}

export default IncidentReport

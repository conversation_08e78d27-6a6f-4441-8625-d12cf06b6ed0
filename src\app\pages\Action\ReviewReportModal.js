import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { ALL_REPORT_DATA_URL, API_URL, GET_USERS_BY_ROLE, INSPECTIONS_ACTIONS_SUBMIT_WITH_ID, OBSERVATION_REVIEWER_LIST_URL, OBSERVATION_REVIEWER_SUBMIT_URL, REPORT_ACTION_REVIEW, REPORT_INCIDENT_ACTIONS_WITH_ID, STATIC_URL } from "../../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../../services/API";
import cogoToast from "cogo-toast";
// import FormBuilder from "../apps/FormBuilder";
// import FormRender from "../apps/FormRender";
// import FormRenderer from "../apps/FormRenderer";
// import Select from 'react-select'
import ReportModal from "../../shared/ReportModal";
import moment from 'moment';
// import ReportModal from "../shared/ReportModal";

const ReportDataModal = ({ data, applicationType, last, showModal, setShowModal }) => {


    const [users, setUsers] = useState([])


    const [formData, setFormData] = useState(
        {
            "date": moment().format('DD/MM/YYYY'),
            "numberOfEmployees": 0,
            "dailyHoursOfEmployee": 0,
            "workingDaysOfEmployee": 0,
            "numberofContractors": 0,
            "dailyHoursOfContractors": 0,
            "workingDaysOfContractors": 0,
            "yearAndMonth": data.dueDate,
            "noOfSafety": 0,
            "noOfToolbox": 0,
            "noOfEhsTraining": 0,
            "noOfInspection": 0,
            "noOfManagmentSiteWalk": 0,
            "noOfAuthority": 0,
            "noOfSafeObservation": 0,
            "noOfRiskObservation": 0,
            "type": "",

        }
    )



    useEffect(() => {
        if (last && showModal) {
            setFormData({
                ...last,
                date: moment().format('DD/MM/YYYY'),
                yearAndMonth: data?.dueDate || last.yearAndMonth,
                type: "monthly"
            });
        }
    }, [last, showModal, data]);


    const handleAddData = async () => {
        // Logic to add new data

        const response = await API.patch(REPORT_ACTION_REVIEW(last.id, data.id), {

         
            "numberOfEmployees": parseInt(formData.numberOfEmployees),
            "dailyHoursOfEmployee": parseInt(formData.dailyHoursOfEmployee),
            "workingDaysOfEmployee": parseInt(formData.workingDaysOfEmployee),
            "numberofContractors": parseInt(formData.numberofContractors),
            "dailyHoursOfContractors": parseInt(formData.dailyHoursOfContractors),
            "workingDaysOfContractors": parseInt(formData.workingDaysOfContractors),
            "yearAndMonth": formData.yearAndMonth,
            "noOfSafety": parseInt(formData.noOfSafety),
            "noOfToolbox": parseInt(formData.noOfToolbox),
            "noOfEhsTraining": parseInt(formData.noOfEhsTraining),
            "noOfInspection": parseInt(formData.noOfInspection),
            "noOfManagmentSiteWalk": parseInt(formData.noOfManagmentSiteWalk),
            "noOfAuthority": parseInt(formData.noOfAuthority),
            "noOfSafeObservation": parseInt(formData.noOfSafeObservation),
            "noOfRiskObservation": parseInt(formData.noOfRiskObservation),
            "type": "monthly",
            "locationOneId": formData.locationOneId,
            "locationTwoId": formData.locationTwoId,
            "locationThreeId": formData.locationThreeId,
            "locationFourId": formData.locationFourId

        })

        if (response.status === 200 || response.status === 204) {
            setShowModal(false)


            cogoToast.success('Submitted!')
        }
    };



    return (
        <>
            <Modal show={showModal} onHide={() => setShowModal(false)}>
                <Modal.Header closeButton>
                    <Modal.Title>Review Reports</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <ReportModal type="monthly" actionData={data} formData={formData} setFormData={setFormData} />
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowModal(false)}>
                        Close
                    </Button>
                    <Button variant="primary" onClick={handleAddData}>
                        Submit
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default ReportDataModal;
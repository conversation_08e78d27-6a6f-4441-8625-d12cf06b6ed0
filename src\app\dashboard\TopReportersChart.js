import React, { useEffect, useState } from "react";
import HighchartsWrapper from "./HighchartsWrapper";

const TopReportersChart = ({ info }) => {
    const [data, setData] = useState({
        totalReporters: 5,
        topReporters: [
            { name: "<PERSON><PERSON>", count: 600 },
            { name: "<PERSON><PERSON> (SafetyOfficer)", count: 185 },
            { name: "<PERSON><PERSON><PERSON>", count: 181 },
            { name: "<PERSON> (SafetyOfficer)", count: 138 },
            { name: "<PERSON><PERSON>", count: 127 },
        ],
    });

    useEffect(() => {
        if (info) setData(info);
    }, [info]);

    // Extract reporter names and counts
    const labels = data.topReporters.map((reporter) => reporter.name);
    const counts = data.topReporters.map((reporter) => reporter.count);

    // Highcharts Configuration for Bar Chart
    const options = {
        chart: {
            type: "bar",
            zoomType: "xy", // 🔹 Enables zooming (drag to zoom)
        },
        title: {
            text: "",
        },
        xAxis: {
            categories: labels,
            title: {
                text: "Reporters",
            },
        },
        yAxis: {
            min: 0,
            title: {
                text: "Count",
            },
        },
        tooltip: {
            shared: true,
            pointFormat: "<b>{series.name}</b>: {point.y}<br/>",
        },
        plotOptions: {
            bar: {
                dataLabels: {
                    enabled: true,
                },
            },
        },
        legend: {
            enabled: false, // No need for legend as there is only one series
        },
        series: [
            {
                name: "Report Counts",
                data: counts,
            },
        ],
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    menuItems: [
                        "downloadPNG",
                        "downloadJPEG",
                        "downloadPDF",
                        "downloadSVG",
                        "separator",
                        "downloadCSV",
                        "downloadXLS",
                    ],
                },
            },
        },
    };

    return (
        <div style={{ width: "100%", margin: "auto" }}>
            {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
        </div>
    );
};

export default TopReportersChart;

import React, { useEffect, useState } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import { subMonths, format, isWithinInterval } from "date-fns";
import moment from "moment";

const MonthlyRollingChart = ({ info, dateRange }) => {
    const [rawData, setRawData] = useState({
        totalMonths: 12,
        monthlyCategoryCounts: {
            "2023-11": { Safety: 1 },
            "2024-02": { Safety: 10 },
            "2024-03": { Safety: 11, Health: 2 },
            "2024-04": { Safety: 313, Health: 2 },
            "2024-05": { Safety: 581, Health: 2, Environment: 1 },
            "2024-06": { Environment: 6, Safety: 203, Health: 2 },
            "2024-07": { Safety: 87 },
            "2024-08": { Safety: 217, Environment: 7, Health: 3 },
            "2024-09": { Environment: 12, Safety: 226, Health: 2 },
            "2024-10": { Safety: 116, Environment: 6 },
            "2024-11": { Safety: 20, Health: 2 },
            "2024-12": { Safety: 14, Health: 6, Environment: 3 },
        },
    });

    useEffect(() => {
        if (info) {
            setRawData(info);
        }
    }, [info]);

    // Generate last 12 months in 'YYYY-MM' format
    const last12Months = Array.from({ length: 12 }, (_, i) =>
        format(subMonths(new Date(), i), "yyyy-MM")
    ).reverse();

    let filteredMonths = last12Months;

    if (dateRange && dateRange.length === 2) {
        let [startDate, endDate] = dateRange.map((date) => new Date(date));

        if (!isNaN(startDate) && !isNaN(endDate) && startDate <= endDate) {
            filteredMonths = last12Months.filter((month) => {
                const monthDate = new Date(`${month}-01`);
                return isWithinInterval(monthDate, { start: startDate, end: endDate });
            });
        }
    }

    // Format labels to 'Jan 2024', 'Feb 2024', etc.
    const formattedLabels = filteredMonths.map((month) =>
        moment(month, "YYYY-MM").format("MMM YYYY")
    );

    const categories = ["Safety", "Health", "Environment"];

    // Extract data for each category
    const seriesData = categories.map((category) => ({
        name: category,
        data: filteredMonths.map((month) => rawData.monthlyCategoryCounts[month]?.[category] || 0),
    }));

    // Highcharts Configuration for Grouped Bar Chart
    const options = {
        chart: {
            type: "column",
            zoomType: "xy", // 🔹 Enables zooming (drag to zoom)
        },
        title: {
            text: "",
        },
        xAxis: {
            categories: formattedLabels,
            title: {
                text: "Months",
            },
            crosshair: true,
        },
        yAxis: {
            min: 0,
            title: {
                text: "Counts",
            },
        },
        tooltip: {
            shared: true,
            pointFormat: "<b>{series.name}</b>: {point.y}<br/>",
        },
        plotOptions: {
            column: {
                grouping: true, // 🔹 Groups bars instead of stacking
                dataLabels: {
                    enabled: true,
                },
            },
        },
        legend: {
            enabled: true,
        },
        series: seriesData,
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    menuItems: [
                        "downloadPNG",
                        "downloadJPEG",
                        "downloadPDF",
                        "downloadSVG",
                        "separator",
                        "downloadCSV",
                        "downloadXLS",
                    ],
                },
            },
        },
    };

    return (
        <div style={{ width: "100%", margin: "auto" }}>
            {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
        </div>
    );
};

export default MonthlyRollingChart;

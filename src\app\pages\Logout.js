import React, {useEffect} from "react";
import { useDispatch } from "react-redux";
import { loginActions } from "../store/login-slice";
import { useHistory } from "react-router-dom";


const Logout = () => {  
    const dispatch = useDispatch();
    const history = useHistory();
    useEffect(() => {
        logout();
    }, [])

    const logout = async () => {
     
        localStorage.removeItem("access_token")
        dispatch(loginActions.setLogout())
        history.push('/login')

    }
    
    return (
        <>
        </>
    )
}

export default Logout;
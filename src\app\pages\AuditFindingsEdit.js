import React, { useEffect, useState } from "react";
import Select from 'react-select';
import { GMS1_URL,STATIC_URL } from "../constants";
import API from "../services/API";
import OpportunityForImprovementForm from "./Audit/OpportunityForImprovementForm";
import NonConformancesForm from "./Audit/NonConformancesForn";
import GoodPracticesForm from "./Audit/GoodPracticesForm";

const AuditFindingsEdit = ({ data, onSave, setFindingsModal, handleFileChange,auditId }) => {

    const [gms, setGms] = useState([])
    const [editFiles, setEditFiles] = useState([])

    useEffect(() => { getGms() }, [])
    const getGms = async () => {
        const response = await API.get(GMS1_URL);
        if (response.status === 200) {
            setGms(response.data)
        }
    }
   

  
    const [selectedCategory, setSelectedCategory] = useState('');
    const [aformData, setAFormData] = useState('');

    useEffect(() => { setAFormData(data); setSelectedCategory(data.category || '') }, [data])
    const handleChange = (e) => {
        const { name, value, type } = e.target;

        if (type === "radio") {
            setAFormData(prevFormData => ({
                ...prevFormData,
                [name]: value
            }));
        } else if (name === 'inspectionCategories') {
            const selectedCategory = gms.find(g => g.id === value);
            setAFormData(prevFormData => ({
                ...prevFormData,
                [name]: { id: value, name: selectedCategory ? selectedCategory.name : '' }
            }));
        } else {
            setAFormData(prevFormData => ({
                ...prevFormData,
                [name]: value
            }));
        }
    };



    const handleCategoryChange = (event) => {
        setSelectedCategory(event.target.value);
        setAFormData(prevFormData => ({
            ...prevFormData,
            category: event.target.value
        }));

    };

    const renderSpecificForm = () => {
        switch (selectedCategory) {
            case 'Good Practices':
                return <GoodPracticesForm gms={gms} aformData={aformData} handleFileChange={handleFileChange} handleChange={handleChange} />;
            case 'Non-Conformances':
                return <NonConformancesForm gms={gms} aformData={aformData} handleFileChange={handleFileChange} handleChange={handleChange} auditId={auditId} files={editFiles}/>;
            case 'Opportunity For Improvement':
                return <OpportunityForImprovementForm gms={gms} aformData={aformData} handleFileChange={handleFileChange} handleChange={handleChange} />;
            default:
                return null;
        }
    };

    // Define the specific forms here similar to the original component
    // ...


    return (
        <form className="forms-sample">
            <div className='form-group mt-3'>
                <label htmlFor="categorySelect">Category:</label>
                <select
                    className="form-control"
                    value={selectedCategory}
                    onChange={handleCategoryChange}
                    id="categorySelect"
                >
                    <option value="">Select Category...</option>
                    <option value="Good Practices">Good Practices</option>
                    <option value="Non-Conformances">Non-Conformances</option>
                    <option value="Opportunity For Improvement">Opportunity For Improvement</option>
                </select>

                {selectedCategory && renderSpecificForm()}

            </div>
            <button type="button" className="btn btn-primary mt-2" onClick={() => onSave(data.id, aformData)}>
                Save Observation
            </button>

            <button type="button" className="btn btn-light mt-2" onClick={() => setFindingsModal(false)}>
                Close
            </button>
        </form>
    );
}

export default AuditFindingsEdit;

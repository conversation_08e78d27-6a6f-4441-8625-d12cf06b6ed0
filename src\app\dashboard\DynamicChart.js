import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import TimeRangeButtons from './TimeRangeButtons'; // Make sure to import the TimeRangeButtons
import { getTrendlinePoints } from './TrendLinePoints';

// Utility function to subtract time from a date
// ... (same subtractDate function as before)



const subtractDate = (date, interval, units) => {
    const ret = new Date(date);
    switch (interval.toLowerCase()) {
        case 'years':
            ret.setFullYear(ret.getFullYear() - units);
            break;
        case 'months':
            ret.setMonth(ret.getMonth() - units);
            break;
        // ... handle other cases as needed
        default:
            throw new Error('Invalid interval');
    }
    return ret;
};

const DynamicChart = ({ rawData, timeline, title, total, inverse, legend }) => {

    const calculateTrendColor = (data) => {
        if (data.length < 2) return 'red'; // Default color if not enough data to determine a trend

        const firstDataPoint = data[0];
        const lastDataPoint = data[data.length - 1];
        return lastDataPoint.value > firstDataPoint.value ? `${inverse ? 'green' : 'red'}` : `${inverse ? 'red' : 'green'}`;
    };

    const isUpwardTrend = () => {
        // Assuming the data is sorted by date, compare the first and last values
        const firstValue = rawData[0]?.value;
        const lastValue = rawData[rawData.length - 1]?.value;
        return lastValue >= firstValue;
    };

    const [lineColor, setLineColor] = useState('red');


    const [chartWidth, setChartWidth] = useState(window.innerWidth);
    useEffect(() => {
        console.log(timeline)
        const handleResize = () => {
            // Set chart width based on the window width or parent container's width
            setChartWidth(window.innerWidth);
        };

        // Add event listener
        window.addEventListener('resize', handleResize);

        // Call handler right away so state gets updated with initial window size
        handleResize();

        // Remove event listener on cleanup
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const [activeTab, setActiveTab] = useState('1Y'); // Default to 3M
    const [filteredData, setFilteredData] = useState(rawData); // State for the filtered data

    // Effect hook to update the chart data when the activeTab changes
    useEffect(() => {
        const currentDate = new Date();
        let startDate;

        switch (activeTab) {
            case '3M':
                startDate = subtractDate(currentDate, 'months', 3);
                break;
            case '6M':
                startDate = subtractDate(currentDate, 'months', 6);
                break;
            case '1Y':
                startDate = subtractDate(currentDate, 'years', 1);
                break;
            case '3Y':
                startDate = subtractDate(currentDate, 'years', 3);
                break;
            case 'Max':
                startDate = new Date(Math.min(...rawData.map((data) => new Date(data.date))));
                break;
            default:
                startDate = currentDate; // Default to current date if for some reason the activeTab is not recognized
        }

        // Filter rawData where the date is greater than or equal to startDate
        const newFilteredData = rawData.filter((data) => new Date(data.date) >= startDate);

        // Check if newFilteredData is empty
        if (newFilteredData.length === 0) {
            // If it is empty, find the most recent data point before the startDate
            const mostRecentData = rawData
                .filter((data) => new Date(data.date) < startDate)
                .reduce((a, b) => (new Date(a.date) > new Date(b.date) ? a : b), {});

            // Set filteredData to an array containing only the most recent data point
            setFilteredData([mostRecentData]);
        } else {
            setFilteredData(newFilteredData);
        }

        const lineColor = calculateTrendColor(newFilteredData);
        setLineColor(lineColor);

    }, [activeTab, rawData]);

    // Callback function to be passed to TimeRangeButtons component
    const onRangeSelected = (range) => {
        setActiveTab(range);
    };

    const trendlinePoints = useMemo(() => getTrendlinePoints(filteredData), [filteredData]);

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', { month: 'short', year: '2-digit' });
    };

    // const getStrokeColor = (point, index) => {
    //     if (index === 0) return 'green'; // Default color for the first point
    //     return point.value >= rawData[index - 1].value ? 'green' : 'red';
    // };

    return (
        <div>
            <div className=''>
                <h4 className='mb-3 text-center'>{title}* {timeline && timeline.monthYearString} : {total}</h4>
                <TimeRangeButtons onRangeSelected={onRangeSelected} />
            </div>


            <ResponsiveContainer width="100%" aspect={4.0 / 2.0}>
                <LineChart width={chartWidth} height={200} data={filteredData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={formatDate} />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                        type="monotone"
                        dataKey="value"
                        name={legend}
                        stroke={lineColor} // Use the determined color for the line
                        activeDot={{ r: 8 }}
                        isAnimationActive={false}
                    />
                    {/* <Line type="linear" dataKey="y" data={trendlinePoints} stroke="#efefef" dot={true} strokeWidth={0.5} /> */}
                </LineChart>
            </ResponsiveContainer>
        </div>
    );
};

// ... Your rawData and App component here

export default DynamicChart;

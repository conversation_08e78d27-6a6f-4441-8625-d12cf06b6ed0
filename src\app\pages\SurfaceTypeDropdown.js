import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    SURFACE_TYPES_URL,

} from '../constants';
import { createAxiosInstanceWithToken } from './TempAxios';

const SurfaceTypeDropdown = ({ incidentData, setIncidentData, readOnly,type }) => {
    const [data, setData] = useState([]);


    const [selected, setSelected] = useState(incidentData.surfaceType ? incidentData.surfaceType.id ?? '' : '');


    const axiosInstance = createAxiosInstanceWithToken();

    useEffect(() => {
        axiosInstance.get(SURFACE_TYPES_URL)
            .then(response => {
                setData(response.data);
            })
            .catch(error => {
                console.error('Error fetching risk categories', error);
            });
    }, []);


    return (
        <div className=''>
            <div className='form-group'>
                <label className=''> Surface Type <span style={{ color: 'red' }}>*</span></label>


                <select className="form-select me-2" disabled={readOnly} value={selected} onChange={(e) => { setSelected(e.target.value); setIncidentData((prev) => ({ ...prev, surfaceTypeId: e.target.value })) }}>
                    <option value="">Select</option>
                    {data.map(i => (
                        <option key={i.id} value={i.id}>{i.name}</option>
                    ))}
                </select>
            </div>



        </div>
    );
};

SurfaceTypeDropdown.defaultProps = {
    readOnly: false
}

export default SurfaceTypeDropdown;
